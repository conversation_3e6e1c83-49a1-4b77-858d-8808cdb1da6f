{% extends "base.html" %}

{% block title %}数据分析 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2>
            <i class="fas fa-chart-line me-2"></i> 数据分析与可视化
        </h2>
        <p class="text-muted">深入分析处理数据的统计信息和趋势</p>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card primary">
            <div class="stat-number text-primary" id="total-movies">--</div>
            <div class="stat-label">电影总数</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card success">
            <div class="stat-number text-success" id="processed-people">--</div>
            <div class="stat-label">已处理人员</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number text-warning" id="unique-people">--</div>
            <div class="stat-label">唯一人员</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card info">
            <div class="stat-number text-info" id="avg-rating">--</div>
            <div class="stat-label">平均评分</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 处理进度图表 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-area"></i> 24小时处理趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="processingTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 任务状态分布 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> 任务状态分布</h5>
            </div>
            <div class="card-body">
                <canvas id="taskStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 字段类型分析 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 字段处理统计</h5>
            </div>
            <div class="card-body">
                <canvas id="fieldStatsChart" width="600" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 系统资源使用 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-server"></i> 系统资源</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">CPU使用率</span>
                        <span class="small" id="cpu-usage">--%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-primary" id="cpu-bar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">内存使用率</span>
                        <span class="small" id="memory-usage">--%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-warning" id="memory-bar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">磁盘使用率</span>
                        <span class="small" id="disk-usage">--%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" id="disk-bar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="small text-muted">
                    <div>运行时间: <span id="system-uptime">--</span></div>
                    <div>总请求数: <span id="total-requests">--</span></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> 处理任务详情</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="exportStats()">
                        <i class="fas fa-download"></i> 导出统计
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="refreshCharts()">
                        <i class="fas fa-sync-alt"></i> 刷新图表
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="tasksTable">
                        <thead class="table-light">
                            <tr>
                                <th>任务ID</th>
                                <th>文件名</th>
                                <th>处理类型</th>
                                <th>状态</th>
                                <th>进度</th>
                                <th>开始时间</th>
                                <th>持续时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tasks-tbody">
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="loading-shimmer rounded" style="height: 40px;"></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let charts = {};

$(document).ready(function() {
    initializeCharts();
    loadAnalyticsData();
    
    // 定时刷新数据
    setInterval(function() {
        loadAnalyticsData();
    }, 5000);
});

function initializeCharts() {
    // 处理趋势图表
    const trendCtx = document.getElementById('processingTrendChart').getContext('2d');
    charts.trendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '已完成',
                data: [],
                borderColor: '#059669',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                tension: 0.4
            }, {
                label: '错误',
                data: [],
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 任务状态饼图
    const statusCtx = document.getElementById('taskStatusChart').getContext('2d');
    charts.statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['运行中', '已完成', '错误', '已暂停'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#2563eb', '#059669', '#dc2626', '#d97706']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // 字段处理统计柱图
    const fieldCtx = document.getElementById('fieldStatsChart').getContext('2d');
    charts.fieldChart = new Chart(fieldCtx, {
        type: 'bar',
        data: {
            labels: ['导演', '编剧', '演员'],
            datasets: [{
                label: '已处理',
                data: [0, 0, 0],
                backgroundColor: '#2563eb'
            }, {
                label: '成功',
                data: [0, 0, 0],
                backgroundColor: '#059669'
            }, {
                label: '错误',
                data: [0, 0, 0],
                backgroundColor: '#dc2626'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadAnalyticsData() {
    Promise.all([
        loadSystemStats(),
        loadPerformanceMetrics(),
        loadTaskDetails()
    ]);
}

function loadSystemStats() {
    return $.get('/api/system_stats').then(function(data) {
        if (data.success) {
            // 更新系统资源显示
            $('#cpu-usage').text(data.system.cpu_percent.toFixed(1) + '%');
            $('#cpu-bar').css('width', data.system.cpu_percent + '%');
            
            $('#memory-usage').text(data.system.memory_percent.toFixed(1) + '%');
            $('#memory-bar').css('width', data.system.memory_percent + '%');
            
            $('#disk-usage').text(data.system.disk_percent.toFixed(1) + '%');
            $('#disk-bar').css('width', data.system.disk_percent + '%');
            
            // 更新统计数字
            const uptime = formatDuration(data.system.uptime_seconds);
            $('#system-uptime').text(uptime);
            $('#total-requests').text(data.stats.total_requests.toLocaleString());
            
            // 更新任务状态图表
            const tasks = data.tasks;
            charts.statusChart.data.datasets[0].data = [
                tasks.running_tasks,
                tasks.completed_tasks,
                tasks.error_tasks,
                tasks.paused_tasks
            ];
            charts.statusChart.update();
        }
    });
}

function loadPerformanceMetrics() {
    return $.get('/api/performance_metrics').then(function(data) {
        if (data.success) {
            // 更新趋势图表
            const hours = Object.keys(data.hourly_stats).reverse();
            const completedData = hours.map(h => data.hourly_stats[h].completed);
            const errorData = hours.map(h => data.hourly_stats[h].errors);
            
            charts.trendChart.data.labels = hours;
            charts.trendChart.data.datasets[0].data = completedData;
            charts.trendChart.data.datasets[1].data = errorData;
            charts.trendChart.update();
        }
    });
}

function loadTaskDetails() {
    return $.get('/api/tasks').then(function(data) {
        if (data.success) {
            // 统计字段处理情况
            const fieldStats = {
                'director': { processed: 0, success: 0, error: 0 },
                'screenwriter': { processed: 0, success: 0, error: 0 },
                'actor': { processed: 0, success: 0, error: 0 }
            };
            
            let totalMovies = 0;
            let totalProcessedPeople = 0;
            
            data.tasks.forEach(function(task) {
                const field = task.field_to_process;
                if (field && fieldStats[field]) {
                    if (task.status === 'completed') {
                        fieldStats[field].success++;
                        if (task.progress_data && task.progress_data.processed_count) {
                            fieldStats[field].processed += task.progress_data.processed_count;
                            totalProcessedPeople += task.progress_data.processed_count;
                        }
                    } else if (task.status === 'error') {
                        fieldStats[field].error++;
                    }
                }
            });
            
            // 更新统计卡片
            $('#total-movies').text(totalMovies.toLocaleString());
            $('#processed-people').text(totalProcessedPeople.toLocaleString());
            $('#unique-people').text('--'); // 需要额外API获取
            $('#avg-rating').text('--'); // 需要额外API获取
            
            // 更新字段统计图表
            charts.fieldChart.data.datasets[0].data = [
                fieldStats.director.processed,
                fieldStats.screenwriter.processed,
                fieldStats.actor.processed
            ];
            charts.fieldChart.data.datasets[1].data = [
                fieldStats.director.success,
                fieldStats.screenwriter.success,
                fieldStats.actor.success
            ];
            charts.fieldChart.data.datasets[2].data = [
                fieldStats.director.error,
                fieldStats.screenwriter.error,
                fieldStats.actor.error
            ];
            charts.fieldChart.update();
            
            // 更新任务表格
            updateTasksTable(data.tasks);
        }
    });
}

function updateTasksTable(tasks) {
    let html = '';
    
    if (tasks.length === 0) {
        html = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无任务记录
                </td>
            </tr>
        `;
    } else {
        tasks.forEach(function(task) {
            const startTime = new Date(task.start_time);
            const duration = task.end_time ? 
                formatDuration((new Date(task.end_time) - startTime) / 1000) : 
                formatDuration((new Date() - startTime) / 1000);
            
            let statusBadge = '';
            switch(task.status) {
                case 'running':
                    statusBadge = '<span class="badge bg-primary">运行中</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge bg-success">已完成</span>';
                    break;
                case 'error':
                    statusBadge = '<span class="badge bg-danger">错误</span>';
                    break;
                case 'paused':
                    statusBadge = '<span class="badge bg-warning">已暂停</span>';
                    break;
                default:
                    statusBadge = '<span class="badge bg-secondary">未知</span>';
            }
            
            const progress = Math.round(task.progress || 0);
            const progressBar = task.status === 'running' ? 
                `<div class="progress" style="height: 4px;">
                    <div class="progress-bar progress-bar-animated" style="width: ${progress}%"></div>
                 </div>
                 <small>${progress}%</small>` :
                `<span class="text-muted">${progress}%</span>`;
            
            const fieldMap = {
                'director': '导演',
                'screenwriter': '编剧', 
                'actor': '演员'
            };
            
            html += `
                <tr>
                    <td><code class="small">${task.task_id}</code></td>
                    <td>${task.filename}</td>
                    <td>${fieldMap[task.field_to_process] || task.field_to_process}</td>
                    <td>${statusBadge}</td>
                    <td>${progressBar}</td>
                    <td class="small">${startTime.toLocaleString()}</td>
                    <td class="small">${duration}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewTaskDetails('${task.task_id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${task.status === 'running' ? `
                                <button class="btn btn-outline-warning" onclick="pauseTask('${task.task_id}')">
                                    <i class="fas fa-pause"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#tasks-tbody').html(html);
}

function refreshCharts() {
    loadAnalyticsData();
}

function exportStats() {
    $.get('/api/system_stats', function(data) {
        if (data.success) {
            const exportData = {
                export_time: new Date().toISOString(),
                system_stats: data,
                tasks: []
            };
            
            $.get('/api/tasks', function(taskData) {
                if (taskData.success) {
                    exportData.tasks = taskData.tasks;
                }
                
                // 创建并下载文件
                const blob = new Blob([JSON.stringify(exportData, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `stats_export_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
    });
}

function viewTaskDetails(taskId) {
    // 这里可以打开详细的任务信息弹窗
    window.open(`/api/task_status/${taskId}`, '_blank');
}

function pauseTask(taskId) {
    if (confirm('确定要暂停这个任务吗？')) {
        $.post(`/api/pause_processing/${taskId}`, function(data) {
            if (data.success) {
                loadAnalyticsData();
            } else {
                alert('暂停失败: ' + data.message);
            }
        });
    }
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}
</script>
{% endblock %}