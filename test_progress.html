<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .status-running { color: #007bff; }
        .status-completed { color: #28a745; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>进度显示测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 处理队列</h5>
                    </div>
                    <div class="card-body">
                        <div id="taskQueue">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bookmark"></i> 断点信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="checkpointInfo">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-success" onclick="simulateProgress()">模拟进度</button>
        </div>
    </div>

    <script>
        function loadTaskQueue() {
            $.get('/api/tasks', function(data) {
                if (data.success) {
                    let html = '';
                    if (data.tasks.length === 0) {
                        html = '<small class="text-muted">暂无处理任务</small>';
                    } else {
                        data.tasks.forEach(function(task) {
                            let statusClass = task.status === 'running' ? 'status-running' : 
                                             task.status === 'completed' ? 'status-completed' : 'status-error';
                            let statusIcon = task.status === 'running' ? 'fa-spinner fa-spin' : 
                                             task.status === 'completed' ? 'fa-check-circle' : 'fa-exclamation-circle';
                            
                            // 添加进度显示
                            let progressHtml = '';
                            if (task.status === 'running' && task.progress) {
                                progressHtml = `
                                    <div class="progress mt-1" style="height: 4px;">
                                        <div class="progress-bar bg-success" style="width: ${task.progress}%"></div>
                                    </div>
                                    <small class="text-muted">${task.progress}% - ${task.message || '正在处理...'}</small>
                                `;
                            } else if (task.status === 'completed') {
                                progressHtml = `<small class="text-success">已完成</small>`;
                            } else if (task.status === 'error') {
                                progressHtml = `<small class="text-danger">错误: ${task.error || '未知错误'}</small>`;
                            }
                            
                            html += `
                                <div class="mb-2 p-2 border rounded">
                                    <div class="d-flex justify-content-between">
                                        <small class="fw-bold">${task.filename}</small>
                                        <i class="fas ${statusIcon} ${statusClass}"></i>
                                    </div>
                                    <small class="text-muted">${new Date(task.start_time).toLocaleString()}</small>
                                    ${progressHtml}
                                </div>
                            `;
                        });
                    }
                    $('#taskQueue').html(html);
                }
            });
        }

        function loadCheckpointInfo() {
            const field = 'actor'; // 默认字段
            $.get('/api/checkpoint/' + field, function(data) {
                if (data.success) {
                    const checkpoint = data.checkpoint;
                    const processedIds = checkpoint.processed_ids || [];
                    const totalCount = checkpoint.total_ids || 0;
                    const processedCount = processedIds.length;
                    const remainingCount = totalCount - processedCount;
                    const progressPercent = totalCount > 0 ? (processedCount / totalCount * 100).toFixed(1) : 0;
                    
                    const fieldNames = {
                        'director': '导演',
                        'screenwriter': '编剧',
                        'actor': '演员'
                    };
                    
                    const html = `
                        <div class="checkpoint-status">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-primary">${fieldNames[field]}</span>
                                <small class="text-muted">${new Date(checkpoint.modified).toLocaleString()}</small>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: ${progressPercent}%"></div>
                            </div>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="h6 mb-0">${processedCount}</div>
                                    <small class="text-muted">已处理</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0">${remainingCount}</div>
                                    <small class="text-muted">剩余</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0">${progressPercent}%</div>
                                    <small class="text-muted">完成率</small>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#checkpointInfo').html(html);
                } else {
                    $('#checkpointInfo').html(`
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle"></i>
                            <small>暂无断点信息</small>
                        </div>
                    `);
                }
            }).fail(function() {
                $('#checkpointInfo').html(`
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle"></i>
                        <small>加载断点信息失败</small>
                    </div>
                `);
            });
        }

        function refreshData() {
            loadTaskQueue();
            loadCheckpointInfo();
        }

        function simulateProgress() {
            // 模拟进度更新
            $.post('/api/simulate_progress', function(data) {
                if (data.success) {
                    alert('进度已更新！');
                    refreshData();
                } else {
                    alert('更新失败: ' + data.message);
                }
            }).fail(function() {
                alert('请求失败');
            });
        }

        // 页面加载时初始化
        $(document).ready(function() {
            refreshData();
            
            // 定时刷新
            setInterval(refreshData, 5000);
        });
    </script>
</body>
</html>