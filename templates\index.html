{% extends "base.html" %}

{% block title %}数据处理仪表盘 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-2">
                    <i class="fas fa-tachometer-alt me-2"></i> 数据处理仪表盘
                </h2>
                <p class="text-muted mb-0">豆瓣电影数据智能处理平台</p>
            </div>
            <div class="text-end">
                <div class="badge bg-success fs-6" id="system-status">
                    <i class="fas fa-circle me-1"></i> 系统运行正常
                </div>
                <div class="text-muted small mt-1" id="last-update">
                    最后更新: <span id="update-time">--</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card primary">
            <div class="stat-number text-primary" id="running-tasks">--</div>
            <div class="stat-label">运行中任务</div>
            <div class="small text-muted mt-2" id="running-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card success">
            <div class="stat-number text-success" id="completed-tasks">--</div>
            <div class="stat-label">已完成任务</div>
            <div class="small text-muted mt-2" id="completed-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number text-warning" id="total-processed">--</div>
            <div class="stat-label">处理总数</div>
            <div class="small text-muted mt-2" id="processed-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card danger">
            <div class="stat-number text-primary" id="success-rate">--%</div>
            <div class="stat-label">成功率</div>
            <div class="small text-muted mt-2" id="rate-detail">加载中...</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 主要内容 -->
    <div class="col-md-8">
        <!-- 快速操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('upload_file') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-upload me-2"></i> 上传新数据文件
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary btn-lg" onclick="useDatasetFile()">
                                <i class="fas fa-database me-2"></i> 使用内置数据集
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('config') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-cogs me-2"></i> 配置管理
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="{{ url_for('results') }}" class="btn btn-outline-info btn-lg">
                                <i class="fas fa-chart-bar me-2"></i> 查看结果
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history"></i> 最近活动</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshActivities()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="recent-activities">
                    <div class="loading-shimmer rounded" style="height: 100px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏信息 -->
    <div class="col-md-4">
        <!-- 当前任务状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-tasks"></i> 当前任务</h5>
            </div>
            <div class="card-body">
                <div id="current-tasks">
                    <div class="loading-shimmer rounded" style="height: 60px;"></div>
                </div>
            </div>
        </div>
        
        <!-- 系统信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 系统信息</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-server text-primary"></i>
                            <div class="small text-muted mt-1">服务器状态</div>
                            <div class="fw-bold text-success" id="server-status">正常</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <i class="fas fa-key text-warning"></i>
                            <div class="small text-muted mt-1">API状态</div>
                            <div class="fw-bold" id="api-status">检查中...</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <i class="fas fa-database text-info"></i>
                            <div class="small text-muted mt-1">数据文件</div>
                            <div class="fw-bold" id="data-files">--</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <i class="fas fa-clock text-secondary"></i>
                            <div class="small text-muted mt-1">运行时间</div>
                            <div class="fw-bold" id="uptime">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据集预览 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> 数据集预览</h5>
            </div>
            <div class="card-body">
                <div id="dataset-preview">
                    <div class="loading-shimmer rounded" style="height: 80px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let startTime = Date.now();

$(document).ready(function() {
    loadDashboardData();
    
    // 定时刷新仪表盘数据
    setInterval(function() {
        loadDashboardData();
        updateUptime();
    }, 3000);
    
    // 立即开始更新运行时间
    updateUptime();
    setInterval(updateUptime, 1000);
});

function loadDashboardData() {
    // 并行加载所有数据
    Promise.all([
        loadCurrentTasks(),
        loadConfigStatus(), 
        loadRecentResults(),
        loadDatasetInfo(),
        loadRecentActivities()
    ]).then(() => {
        $('#update-time').text(new Date().toLocaleTimeString());
    });
}

function loadCurrentTasks() {
    return $.get('/api/tasks').then(function(data) {
        if (data.success) {
            const runningTasks = data.tasks.filter(t => t.status === 'running');
            const completedTasks = data.tasks.filter(t => t.status === 'completed');
            const totalProcessed = data.tasks.reduce((sum, t) => {
                if (t.progress_data && t.progress_data.processed_count) {
                    return sum + t.progress_data.processed_count;
                }
                return sum;
            }, 0);
            
            const totalCount = data.tasks.reduce((sum, t) => {
                if (t.progress_data && t.progress_data.total_count) {
                    return sum + t.progress_data.total_count;
                }
                return sum;
            }, 0);
            
            const successRate = totalCount > 0 ? (totalProcessed / totalCount * 100) : 0;
            
            // 更新统计数字
            $('#running-tasks').text(runningTasks.length);
            $('#completed-tasks').text(completedTasks.length);
            $('#total-processed').text(totalProcessed.toLocaleString());
            $('#success-rate').text(successRate.toFixed(1) + '%');
            
            // 更新详细信息
            $('#running-detail').text(runningTasks.length > 0 ? '有任务正在处理' : '暂无运行任务');
            $('#completed-detail').text(`今日完成 ${completedTasks.length} 个`);
            $('#processed-detail').text(`累计处理 ${totalProcessed.toLocaleString()} 条`);
            $('#rate-detail').text(totalCount > 0 ? '处理质量良好' : '暂无数据');
            
            // 显示当前任务
            let currentTasksHtml = '';
            if (runningTasks.length === 0) {
                currentTasksHtml = `
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <div>暂无运行中的任务</div>
                        <small>点击上方按钮开始新的处理任务</small>
                    </div>
                `;
            } else {
                runningTasks.forEach(function(task) {
                    const progress = Math.round(task.progress || 0);
                    currentTasksHtml += `
                        <div class="task-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="fw-bold">${task.filename}</div>
                                <span class="badge bg-primary">运行中</span>
                            </div>
                            <div class="progress mb-2" style="height: 6px;">
                                <div class="progress-bar progress-bar-animated" style="width: ${progress}%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">${task.message || '正在处理...'}</small>
                                <small class="text-muted">${progress}%</small>
                            </div>
                        </div>
                    `;
                });
            }
            $('#current-tasks').html(currentTasksHtml);
        }
    });
}

function loadConfigStatus() {
    return $.get('/api/config').then(function(data) {
        let statusHtml = '';
        let statusClass = '';
        
        if (data && data.api_keys && data.api_keys.length > 0 && data.api_keys[0] !== '请在此处填入您的智谱GLM4.5 API密钥') {
            statusHtml = '<i class="fas fa-check-circle text-success me-1"></i> API已配置';
            statusClass = 'text-success';
            $('#api-status').html('<span class="text-success">已配置</span>');
        } else {
            statusHtml = '<i class="fas fa-exclamation-triangle text-warning me-1"></i> 需要配置API';
            statusClass = 'text-warning';
            $('#api-status').html('<span class="text-warning">未配置</span>');
        }
        
        $('#config-status').html(`<div class="${statusClass}">${statusHtml}</div>`);
    });
}

function loadRecentResults() {
    return $.get('/api/results').then(function(data) {
        $('#data-files').text(data.success ? data.results.length : 0);
    });
}

function loadDatasetInfo() {
    return $.get('/api/dataset_info').then(function(data) {
        if (data.success) {
            const info = data.data;
            const html = `
                <div class="small">
                    <div class="d-flex justify-content-between mb-1">
                        <span>记录数:</span>
                        <strong>${info.total_records.toLocaleString()}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>文件大小:</span>
                        <strong>${info.size_mb} MB</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>字段数:</span>
                        <strong>${info.columns.length}</strong>
                    </div>
                </div>
            `;
            $('#dataset-preview').html(html);
        } else {
            $('#dataset-preview').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-exclamation-circle mb-2"></i>
                    <div>数据集未找到</div>
                    <small>请先上传数据文件</small>
                </div>
            `);
        }
    });
}

function loadRecentActivities() {
    return $.get('/api/tasks').then(function(data) {
        if (data.success && data.tasks.length > 0) {
            const recentTasks = data.tasks
                .sort((a, b) => new Date(b.start_time) - new Date(a.start_time))
                .slice(0, 5);
            
            let html = '';
            recentTasks.forEach(function(task) {
                const timeAgo = getTimeAgo(new Date(task.start_time));
                let statusBadge = '';
                let icon = '';
                
                switch(task.status) {
                    case 'running':
                        statusBadge = 'bg-primary';
                        icon = 'fa-spinner fa-spin';
                        break;
                    case 'completed':
                        statusBadge = 'bg-success';
                        icon = 'fa-check-circle';
                        break;
                    case 'error':
                        statusBadge = 'bg-danger';
                        icon = 'fa-exclamation-circle';
                        break;
                    default:
                        statusBadge = 'bg-secondary';
                        icon = 'fa-clock';
                }
                
                html += `
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0 me-3">
                            <div class="rounded-circle bg-light p-2" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas ${icon} text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="fw-medium">${task.filename}</div>
                                <span class="badge ${statusBadge} small">${getStatusText(task.status)}</span>
                            </div>
                            <div class="small text-muted">
                                ${task.field_to_process === 'director' ? '导演' : 
                                  task.field_to_process === 'screenwriter' ? '编剧' : '演员'} 
                                • ${timeAgo}
                            </div>
                            ${task.progress !== undefined ? `
                                <div class="progress mt-1" style="height: 3px;">
                                    <div class="progress-bar" style="width: ${task.progress}%"></div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });
            
            $('#recent-activities').html(html);
        } else {
            $('#recent-activities').html(`
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <div>暂无活动记录</div>
                    <small>开始第一个处理任务来查看活动</small>
                </div>
            `);
        }
    });
}

function useDatasetFile() {
    if (confirm('使用内置数据集 (all_data.pkl) 开始处理？')) {
        window.location.href = '/process/all_data.pkl';
    }
}

function refreshActivities() {
    $('#recent-activities').html('<div class="loading-shimmer rounded" style="height: 100px;"></div>');
    loadRecentActivities();
}

function updateUptime() {
    const uptime = Math.floor((Date.now() - startTime) / 1000);
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = uptime % 60;
    
    $('#uptime').text(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
}

function getTimeAgo(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
}

function getStatusText(status) {
    const statusMap = {
        'running': '运行中',
        'completed': '已完成',
        'error': '错误',
        'paused': '已暂停',
        'stopped': '已停止'
    };
    return statusMap[status] || status;
}
</script>
{% endblock %}