#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API路由
"""
import requests
import json
import time

def test_api(base_url, endpoint, method='GET', data=None):
    """测试单个API端点"""
    url = f"{base_url}{endpoint}"
    
    try:
        if method == 'GET':
            response = requests.get(url, timeout=5)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=5)
        
        print(f"测试 {method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应: {response.text}")
        else:
            print(f"错误响应: {response.text}")
        
        print("-" * 50)
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"测试 {method} {url} 失败: {e}")
        print("-" * 50)
        return False

def main():
    """主测试函数"""
    base_url = "http://localhost:5000"
    
    print("开始测试API路由...\n")
    
    # 测试API列表
    apis = [
        ('/api/config', 'GET'),
        ('/api/tasks', 'GET'),
        ('/api/results', 'GET'),
        ('/api/config', 'POST', {"test": "data"})
    ]
    
    success_count = 0
    total_count = len(apis)
    
    for endpoint, method, *data in apis:
        test_data = data[0] if data else None
        if test_api(base_url, endpoint, method, test_data):
            success_count += 1
        time.sleep(1)  # 避免请求过快
    
    print(f"\n测试结果: {success_count}/{total_count} 个API测试通过")
    
    if success_count == total_count:
        print("所有API测试通过！")
    else:
        print("部分API测试失败，请检查Web应用是否正常运行。")

if __name__ == "__main__":
    main()