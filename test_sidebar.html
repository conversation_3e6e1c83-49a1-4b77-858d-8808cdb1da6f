<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左侧栏测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .status-running { color: #007bff; }
        .status-completed { color: #28a745; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-4">
                <h4>左侧栏测试</h4>
                
                <!-- 处理队列 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 处理队列</h5>
                    </div>
                    <div class="card-body">
                        <div id="taskQueue">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 断点信息 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-bookmark"></i> 断点信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="checkpointInfo">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="loadTaskQueue()">刷新任务队列</button>
                    <button class="btn btn-info" onclick="loadCheckpointInfo()">刷新断点信息</button>
                    <button class="btn btn-success" onclick="testAPI()">测试API</button>
                </div>
            </div>
            
            <div class="col-md-8">
                <h4>调试信息</h4>
                <div class="card">
                    <div class="card-body">
                        <div id="debugInfo">
                            <p>点击按钮测试功能...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function loadTaskQueue() {
            console.log('开始加载任务队列...');
            $('#debugInfo').append('<p>正在调用任务队列API...</p>');
            
            $.get('/api/tasks', function(data) {
                console.log('任务队列API响应:', data);
                $('#debugInfo').append('<p>API响应成功: ' + JSON.stringify(data) + '</p>');
                
                if (data.success) {
                    let html = '';
                    if (data.tasks.length === 0) {
                        html = '<small class="text-muted">暂无处理任务</small>';
                    } else {
                        data.tasks.forEach(function(task) {
                            console.log('处理任务:', task);
                            let statusClass = task.status === 'running' ? 'status-running' : 
                                             task.status === 'completed' ? 'status-completed' : 'status-error';
                            let statusIcon = task.status === 'running' ? 'fa-spinner fa-spin' : 
                                             task.status === 'completed' ? 'fa-check-circle' : 'fa-exclamation-circle';
                            
                            // 添加进度显示
                            let progressHtml = '';
                            if (task.status === 'running' && task.progress) {
                                progressHtml = `
                                    <div class="progress mt-1" style="height: 4px;">
                                        <div class="progress-bar bg-success" style="width: ${task.progress}%"></div>
                                    </div>
                                    <small class="text-muted">${task.progress}% - ${task.message || '正在处理...'}</small>
                                `;
                            } else if (task.status === 'completed') {
                                progressHtml = `<small class="text-success">已完成</small>`;
                            } else if (task.status === 'error') {
                                progressHtml = `<small class="text-danger">错误: ${task.error || '未知错误'}</small>`;
                            }
                            
                            html += `
                                <div class="mb-2 p-2 border rounded">
                                    <div class="d-flex justify-content-between">
                                        <small class="fw-bold">${task.filename}</small>
                                        <i class="fas ${statusIcon} ${statusClass}"></i>
                                    </div>
                                    <small class="text-muted">${new Date(task.start_time).toLocaleString()}</small>
                                    ${progressHtml}
                                </div>
                            `;
                        });
                    }
                    $('#taskQueue').html(html);
                    $('#debugInfo').append('<p>任务队列HTML已更新</p>');
                } else {
                    $('#taskQueue').html('<small class="text-danger">加载失败</small>');
                    $('#debugInfo').append('<p>任务队列加载失败</p>');
                }
            }).fail(function(xhr, status, error) {
                console.error('任务队列API调用失败:', error);
                $('#taskQueue').html('<small class="text-danger">连接失败</small>');
                $('#debugInfo').append('<p>任务队列API调用失败: ' + error + '</p>');
            });
        }

        function loadCheckpointInfo() {
            console.log('开始加载断点信息...');
            $('#debugInfo').append('<p>正在调用断点信息API...</p>');
            
            const field = 'actor';
            $.get('/api/checkpoint/' + field, function(data) {
                console.log('断点信息API响应:', data);
                $('#debugInfo').append('<p>断点API响应成功: ' + JSON.stringify(data) + '</p>');
                
                if (data.success) {
                    const checkpoint = data.checkpoint;
                    const processedIds = checkpoint.processed_ids || [];
                    const totalCount = checkpoint.total_ids || 0;
                    const processedCount = processedIds.length;
                    const remainingCount = totalCount - processedCount;
                    const progressPercent = totalCount > 0 ? (processedCount / totalCount * 100).toFixed(1) : 0;
                    
                    const fieldNames = {
                        'director': '导演',
                        'screenwriter': '编剧',
                        'actor': '演员'
                    };
                    
                    const html = `
                        <div class="checkpoint-status">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-primary">${fieldNames[field]}</span>
                                <small class="text-muted">${new Date(checkpoint.modified).toLocaleString()}</small>
                            </div>
                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: ${progressPercent}%"></div>
                            </div>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="h6 mb-0">${processedCount}</div>
                                    <small class="text-muted">已处理</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0">${remainingCount}</div>
                                    <small class="text-muted">剩余</small>
                                </div>
                                <div class="col-4">
                                    <div class="h6 mb-0">${progressPercent}%</div>
                                    <small class="text-muted">完成率</small>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#checkpointInfo').html(html);
                    $('#debugInfo').append('<p>断点信息HTML已更新</p>');
                } else {
                    $('#checkpointInfo').html('<small class="text-muted">暂无断点信息</small>');
                    $('#debugInfo').append('<p>暂无断点信息</p>');
                }
            }).fail(function(xhr, status, error) {
                console.error('断点信息API调用失败:', error);
                $('#checkpointInfo').html('<small class="text-danger">连接失败</small>');
                $('#debugInfo').append('<p>断点信息API调用失败: ' + error + '</p>');
            });
        }

        function testAPI() {
            console.log('测试API连接...');
            $('#debugInfo').append('<p>开始测试API连接...</p>');
            
            // 测试基本连接
            $.get('/api/tasks', function(data) {
                console.log('API测试成功:', data);
                $('#debugInfo').append('<p>✅ API连接成功</p>');
                $('#debugInfo').append('<p>任务数据: ' + JSON.stringify(data, null, 2) + '</p>');
            }).fail(function(xhr, status, error) {
                console.error('API测试失败:', error);
                $('#debugInfo').append('<p>❌ API连接失败: ' + error + '</p>');
            });
        }

        // 页面加载时初始化
        $(document).ready(function() {
            console.log('页面加载完成');
            $('#debugInfo').append('<p>✅ 页面加载完成</p>');
            $('#debugInfo').append('<p>✅ jQuery加载完成</p>');
            
            // 自动加载一次
            setTimeout(function() {
                loadTaskQueue();
                loadCheckpointInfo();
            }, 1000);
        });
    </script>
</body>
</html>