#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web API端点
"""

import requests
import json
import time

def test_api_endpoints():
    """测试所有API端点"""
    base_url = "http://127.0.0.1:5000"
    
    # 等待服务器启动
    print("⏳ 等待Web服务器启动...")
    time.sleep(3)
    
    endpoints = [
        '/api/tasks',
        '/api/system_stats', 
        '/api/dataset_info',
        '/api/performance_metrics',
        '/api/logs',
        '/api/checkpoints',
        '/api/results'
    ]
    
    print(f"🔍 测试API端点...")
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"\n📡 测试: {endpoint}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success', True):  # 有些端点可能没有success字段
                        print(f"  ✅ 成功 - 状态码: {response.status_code}")
                        if 'data' in data:
                            print(f"     数据类型: {type(data['data'])}")
                        elif 'message' in data:
                            print(f"     消息: {data['message'][:50]}...")
                    else:
                        print(f"  ⚠️  API返回失败 - 消息: {data.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"  ⚠️  响应不是有效JSON")
            else:
                print(f"  ❌ 失败 - 状态码: {response.status_code}")
                print(f"     响应: {response.text[:100]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时")
        except Exception as e:
            print(f"  ❌ 错误: {str(e)}")
    
    # 特别测试dataset_info端点
    print(f"\n📊 详细测试dataset_info端点:")
    try:
        response = requests.get(f"{base_url}/api/dataset_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                info = data['data']
                print(f"  ✅ 数据集信息获取成功:")
                print(f"     记录数: {info.get('total_records', 'N/A')}")
                print(f"     文件大小: {info.get('size_mb', 'N/A')} MB")
                print(f"     字段数: {len(info.get('columns', []))}")
                print(f"     字段统计: {list(info.get('field_stats', {}).keys())}")
            else:
                print(f"  ❌ 数据集信息获取失败: {data.get('message')}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")

def test_web_interface():
    """测试Web界面"""
    base_url = "http://127.0.0.1:5000"
    
    print(f"\n🌐 测试Web界面:")
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print(f"  ✅ 主页加载成功")
            if 'GLM4.5' in response.text or 'dashboard' in response.text.lower():
                print(f"  ✅ 页面内容正常")
            else:
                print(f"  ⚠️  页面内容可能异常")
        else:
            print(f"  ❌ 主页加载失败 - 状态码: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 错误: {str(e)}")

def suggest_fixes():
    """建议修复方案"""
    print(f"\n💡 JavaScript错误修复建议:")
    print(f"  1. 确保Web服务器正在运行 (http://127.0.0.1:5000)")
    print(f"  2. 检查浏览器控制台的具体错误信息")
    print(f"  3. 尝试刷新页面或清除浏览器缓存")
    print(f"  4. 检查网络连接和防火墙设置")
    print(f"  5. 如果问题持续，可以尝试重启Web服务器")
    
    print(f"\n🔧 故障排除步骤:")
    print(f"  1. 打开浏览器开发者工具 (F12)")
    print(f"  2. 查看Console标签页的错误详情")
    print(f"  3. 查看Network标签页检查API请求状态")
    print(f"  4. 确认所有API端点都返回正确的JSON格式")

def main():
    """主函数"""
    print("🚀 开始测试Web API...")
    
    test_api_endpoints()
    test_web_interface()
    suggest_fixes()
    
    print(f"\n✅ 测试完成")
    print(f"\n🌐 Web界面地址: http://127.0.0.1:5000")
    print(f"📱 如果仍有问题，请检查浏览器控制台的具体错误信息")

if __name__ == "__main__":
    main()
