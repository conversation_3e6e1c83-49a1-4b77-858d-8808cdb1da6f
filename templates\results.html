{% extends "base.html" %}

{% block title %}处理结果 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2>
                    <i class="fas fa-chart-bar me-2"></i> 处理结果
                </h2>
                <p class="text-muted mb-0">查看和管理数据处理结果</p>
            </div>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="refreshResults()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <div class="btn-group">
                    <button class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i> 批量操作
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="downloadAll()">下载全部文件</a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportResults('csv')">导出为CSV</a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportResults('json')">导出为JSON</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="clearAllResults()">清理所有结果</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card primary">
            <div class="stat-number text-primary" id="total-files">--</div>
            <div class="stat-label">结果文件</div>
            <div class="small text-muted mt-1" id="files-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card success">
            <div class="stat-number text-success" id="total-records">--</div>
            <div class="stat-label">处理记录</div>
            <div class="small text-muted mt-1" id="records-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number text-warning" id="total-size">--</div>
            <div class="stat-label">文件总大小</div>
            <div class="small text-muted mt-1" id="size-detail">加载中...</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card info">
            <div class="stat-number text-info" id="success-rate">--%</div>
            <div class="stat-label">处理成功率</div>
            <div class="small text-muted mt-1" id="rate-detail">加载中...</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 结果文件列表 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-folder-open"></i> 结果文件</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="fileTypeFilter" onchange="filterResults()">
                            <option value="all">所有文件</option>
                            <option value="results">结果文件</option>
                            <option value="errors">错误文件</option>
                            <option value="checkpoints">断点文件</option>
                        </select>
                        <select class="form-select form-select-sm" id="sortBy" onchange="sortResults()">
                            <option value="date_desc">最新优先</option>
                            <option value="date_asc">最旧优先</option>
                            <option value="name_asc">名称A-Z</option>
                            <option value="size_desc">大小降序</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="resultsList">
                    <div class="loading-shimmer rounded" style="height: 200px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-md-4">
        <!-- 文件预览 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-eye"></i> 文件预览</h5>
            </div>
            <div class="card-body">
                <div id="filePreview">
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <div>点击文件查看预览</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据统计 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> 数据统计</h5>
            </div>
            <div class="card-body">
                <canvas id="dataStatsChart" width="300" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 文件详情弹窗 -->
<div class="modal fade" id="fileDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">文件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="file-detail-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="downloadCurrentFile">
                    <i class="fas fa-download"></i> 下载文件
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let allResults = [];
let currentFile = null;
let dataChart = null;

$(document).ready(function() {
    initializeChart();
    loadResults();
    
    // 定时刷新
    setInterval(function() {
        loadResults();
    }, 10000);
});

function initializeChart() {
    const ctx = document.getElementById('dataStatsChart').getContext('2d');
    dataChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['导演', '编剧', '演员', '错误'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function loadResults() {
    $.get('/api/results').then(function(data) {
        if (data.success) {
            allResults = data.results;
            updateResultsStats(data.results);
            displayResults(data.results);
        } else {
            $('#resultsList').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.message}
                </div>
            `);
        }
    });
}

function updateResultsStats(results) {
    const totalFiles = results.length;
    const totalSize = results.reduce((sum, r) => sum + r.size, 0);
    const totalRecords = results.reduce((sum, r) => sum + (r.record_count || 0), 0);
    
    // 统计不同类型的文件
    const directorFiles = results.filter(r => r.filename.includes('director')).length;
    const screenwriterFiles = results.filter(r => r.filename.includes('screenwriter')).length;
    const actorFiles = results.filter(r => r.filename.includes('actor')).length;
    const errorFiles = results.filter(r => r.filename.includes('error')).length;
    
    // 更新统计卡片
    $('#total-files').text(totalFiles);
    $('#total-records').text(totalRecords.toLocaleString());
    $('#total-size').text((totalSize / 1024 / 1024).toFixed(1) + ' MB');
    $('#success-rate').text('96.8%'); // 模拟数据
    
    $('#files-detail').text(`包含${totalFiles}个结果文件`);
    $('#records-detail').text(`累计处理${totalRecords.toLocaleString()}条记录`);
    $('#size-detail').text(`平均${(totalSize / totalFiles / 1024 / 1024).toFixed(1)}MB/文件`);
    $('#rate-detail').text('处理质量良好');
    
    // 更新图表
    if (dataChart) {
        dataChart.data.datasets[0].data = [directorFiles, screenwriterFiles, actorFiles, errorFiles];
        dataChart.update();
    }
}

function displayResults(results) {
    let html = '';
    
    if (results.length === 0) {
        html = `
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">暂无处理结果</h5>
                <p class="text-muted">上传数据文件并开始处理后，结果将显示在这里</p>
                <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i> 开始处理
                </a>
            </div>
        `;
    } else {
        results.forEach(function(result) {
            const fileSize = (result.size / 1024 / 1024).toFixed(2);
            const createdDate = new Date(result.created).toLocaleString();
            const modifiedDate = new Date(result.modified).toLocaleString();
            
            // 确定文件类型和图标
            let fileIcon = 'fa-file';
            let fileType = '文件';
            let statusBadge = 'bg-secondary';
            
            if (result.filename.includes('results')) {
                fileIcon = 'fa-file-csv';
                fileType = '结果文件';
                statusBadge = 'bg-success';
            } else if (result.filename.includes('errors')) {
                fileIcon = 'fa-exclamation-triangle';
                fileType = '错误文件';
                statusBadge = 'bg-danger';
            } else if (result.filename.includes('checkpoint')) {
                fileIcon = 'fa-bookmark';
                fileType = '断点文件';
                statusBadge = 'bg-warning';
            }
            
            html += `
                <div class="card mb-3 file-item" data-type="${fileType}">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-1 text-center">
                                <i class="fas ${fileIcon} fa-2x text-primary"></i>
                            </div>
                            <div class="col-md-7">
                                <h6 class="mb-1">${result.filename}</h6>
                                <div class="small text-muted">
                                    <span class="badge ${statusBadge} me-2">${fileType}</span>
                                    ${fileSize} MB • 创建: ${createdDate}
                                </div>
                                <div class="small text-muted">
                                    修改: ${modifiedDate}
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-info" onclick="previewFile('${result.filepath}', '${result.filename}')">
                                        <i class="fas fa-eye"></i> 预览
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadFile('${result.filepath}', '${result.filename}')">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="analyzeFile('${result.filepath}', '${result.filename}')">
                                        <i class="fas fa-chart-line"></i> 分析
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('${result.filepath}', '${result.filename}')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#resultsList').html(html);
}

function filterResults() {
    const filter = $('#fileTypeFilter').val();
    
    if (filter === 'all') {
        $('.file-item').show();
    } else {
        $('.file-item').hide();
        $(`.file-item[data-type*="${filter}"]`).show();
    }
}

function sortResults() {
    const sortBy = $('#sortBy').val();
    let sortedResults = [...allResults];
    
    switch(sortBy) {
        case 'date_desc':
            sortedResults.sort((a, b) => new Date(b.modified) - new Date(a.modified));
            break;
        case 'date_asc':
            sortedResults.sort((a, b) => new Date(a.modified) - new Date(b.modified));
            break;
        case 'name_asc':
            sortedResults.sort((a, b) => a.filename.localeCompare(b.filename));
            break;
        case 'size_desc':
            sortedResults.sort((a, b) => b.size - a.size);
            break;
    }
    
    displayResults(sortedResults);
}

function previewFile(filepath, filename) {
    currentFile = {filepath, filename};
    
    // 显示加载状态
    $('#file-detail-content').html('<div class="text-center py-4"><div class="loading-shimmer rounded" style="height: 100px;"></div></div>');
    $('#fileDetailModal').modal('show');
    
    // 获取文件预览
    $.get(`/api/file_preview?file=${encodeURIComponent(filepath)}`).then(function(data) {
        if (data.success) {
            let html = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>文件名:</strong> ${filename}
                    </div>
                    <div class="col-md-6">
                        <strong>大小:</strong> ${(data.file_info.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>记录数:</strong> ${data.file_info.record_count || '--'}
                    </div>
                    <div class="col-md-6">
                        <strong>修改时间:</strong> ${new Date(data.file_info.modified).toLocaleString()}
                    </div>
                </div>
            `;
            
            if (data.preview) {
                html += `
                    <h6>文件内容预览 (前10行):</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                `;
                
                // 添加表头
                if (data.preview.length > 0) {
                    Object.keys(data.preview[0]).forEach(key => {
                        html += `<th>${key}</th>`;
                    });
                }
                
                html += `
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // 添加数据行
                data.preview.forEach(row => {
                    html += '<tr>';
                    Object.values(row).forEach(value => {
                        const cellValue = String(value).length > 50 ? 
                            String(value).substring(0, 50) + '...' : value;
                        html += `<td class="small">${cellValue}</td>`;
                    });
                    html += '</tr>';
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            $('#file-detail-content').html(html);
        } else {
            $('#file-detail-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 预览失败: ${data.message}
                </div>
            `);
        }
    });
}

function downloadFile(filepath, filename) {
    const link = document.createElement('a');
    link.href = `/api/download?file=${encodeURIComponent(filepath)}`;
    link.download = filename;
    link.click();
}

function analyzeFile(filepath, filename) {
    // 跳转到分析页面
    window.location.href = `/analytics?file=${encodeURIComponent(filepath)}`;
}

function deleteFile(filepath, filename) {
    if (confirm(`确定要删除文件 "${filename}" 吗？此操作不可恢复。`)) {
        $.ajax({
            url: '/api/delete_file',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({filepath: filepath}),
            success: function(data) {
                if (data.success) {
                    showAlert('success', '文件删除成功');
                    loadResults();
                } else {
                    showAlert('danger', '删除失败: ' + data.message);
                }
            },
            error: function() {
                showAlert('danger', '网络错误，请稍后重试');
            }
        });
    }
}

function refreshResults() {
    $('#resultsList').html('<div class="loading-shimmer rounded" style="height: 200px;"></div>');
    loadResults();
}

function downloadAll() {
    if (allResults.length === 0) {
        showAlert('warning', '没有可下载的文件');
        return;
    }
    
    if (confirm(`确定要下载所有 ${allResults.length} 个文件吗？`)) {
        allResults.forEach(result => {
            setTimeout(() => downloadFile(result.filepath, result.filename), 100);
        });
    }
}

function exportResults(format) {
    $.post('/api/export_results', JSON.stringify({format: format}), function(data) {
        if (data.success) {
            showAlert('success', `${format.toUpperCase()}导出成功: ${data.export_file}`);
            downloadFile(data.export_file, data.export_file.split('/').pop());
        } else {
            showAlert('danger', '导出失败: ' + data.message);
        }
    }, 'json');
}

function clearAllResults() {
    if (confirm('确定要清理所有结果文件吗？此操作不可恢复！')) {
        $.post('/api/clear_results', function(data) {
            if (data.success) {
                showAlert('success', '所有结果文件已清理');
                loadResults();
            } else {
                showAlert('danger', '清理失败: ' + data.message);
            }
        }, 'json');
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.row').first().prepend(`<div class="col-12">${alertHtml}</div>`);
    
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

// 模态框下载按钮事件
$('#downloadCurrentFile').click(function() {
    if (currentFile) {
        downloadFile(currentFile.filepath, currentFile.filename);
    }
});
</script>
{% endblock %}