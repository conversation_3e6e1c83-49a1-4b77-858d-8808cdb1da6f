#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试电视剧数据处理
"""

import pandas as pd
import json
from pathlib import Path
import sys
import os

def test_tv_data_processing():
    """测试电视剧数据处理配置"""
    print("=== 测试电视剧数据处理配置 ===")
    
    # 1. 加载数据集
    dataset_path = Path("dataset/all_data.pkl")
    if not dataset_path.exists():
        print(f"❌ 数据文件不存在: {dataset_path}")
        return False
    
    try:
        df = pd.read_pickle(dataset_path)
        print(f"✅ 数据加载成功，总记录数: {len(df)}")
        
        # 2. 检查电视剧相关字段
        print(f"\n📺 电视剧字段检查:")
        tv_fields = {
            'director': 'daoyan',  # 导演
            'writer': 'bianjv',    # 编剧  
            'cast': 'zhuyan'       # 演员
        }
        
        for field_name, column_name in tv_fields.items():
            if column_name in df.columns:
                non_null = df[column_name].notna().sum()
                sample_data = df[column_name].dropna().head(3).tolist()
                print(f"  ✅ {field_name:8s} ({column_name:6s}): {non_null:6d} 条有效数据")
                if sample_data:
                    print(f"     示例: {sample_data[:2]}")  # 只显示前2个示例
            else:
                print(f"  ❌ {field_name:8s} ({column_name:6s}): 字段不存在")
        
        # 3. 检查配置文件
        print(f"\n⚙️  配置文件检查:")
        config_files = ["infini_config.json", "infini_config_tv.json"]
        
        for config_file in config_files:
            config_path = Path(config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"  📋 {config_file}:")
                print(f"     data_type: {config.get('data_type', '未设置（默认movie）')}")
                print(f"     field_to_process: {config.get('field_to_process', '未设置')}")
                print(f"     min_reviews: {config.get('min_reviews', '未设置')}")
                print(f"     min_short_reviews: {config.get('min_short_reviews', '未设置')}")
                
                # 检查配置是否适合电视剧
                if config.get('data_type') == 'tv':
                    print(f"     ✅ 已配置为电视剧数据类型")
                else:
                    print(f"     ⚠️  未配置为电视剧数据类型")
            else:
                print(f"  ❌ {config_file}: 文件不存在")
        
        # 4. 测试处理器初始化
        print(f"\n🔧 处理器初始化测试:")
        try:
            from infini_processor_v2 import InfiniDirectorProcessor
            
            # 使用电视剧配置
            config_path = "infini_config_tv.json"
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                processor = InfiniDirectorProcessor(
                    api_keys=config["api_keys"],
                    data_path="dataset/all_data.pkl",
                    output_dir=config.get("output_dir", "infini_output"),
                    max_workers=1,  # 测试时使用较小的并发数
                    batch_size=10,  # 测试时使用较小的批次
                    field_to_process=config.get("field_to_process", "director"),
                    data_type="tv",  # 明确指定为电视剧
                    test_mode=True,  # 启用测试模式
                    test_limit=5,   # 只处理5条记录
                    min_reviews=config.get("min_reviews", 0),
                    min_short_reviews=config.get("min_short_reviews", 0),
                    retry_refused=False,
                    retry_failed=False,
                    config=config
                )
                
                print(f"  ✅ 处理器初始化成功")
                print(f"     数据类型: {processor.data_type}")
                print(f"     处理字段: {processor.field_to_process}")
                print(f"     字段映射: {getattr(processor, 'field_mappings', '未找到字段映射')}")
                
                # 加载数据测试
                processor.load_data()
                print(f"  ✅ 数据加载成功，记录数: {len(processor.df)}")

                # 检查数据中的导演字段
                if hasattr(processor, 'df') and 'daoyan' in processor.df.columns:
                    director_count = processor.df['daoyan'].notna().sum()
                    print(f"  📊 导演字段有效数据: {director_count} 条记录")
                
                return True
            else:
                print(f"  ❌ 配置文件不存在: {config_path}")
                return False
                
        except Exception as e:
            print(f"  ❌ 处理器初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def suggest_tv_usage():
    """建议电视剧数据的使用方法"""
    print(f"\n💡 电视剧数据使用建议:")
    print(f"  1. 使用电视剧配置文件:")
    print(f"     python infini_processor_v2.py --config infini_config_tv.json --data_path dataset/all_data.pkl")
    
    print(f"\n  2. 支持的字段类型:")
    print(f"     - director: 处理导演信息")
    print(f"     - writer: 处理编剧信息") 
    print(f"     - cast: 处理演员信息")
    
    print(f"\n  3. Web界面使用:")
    print(f"     - 启动: python web_app.py")
    print(f"     - 在处理页面选择数据类型为'tv'")
    print(f"     - 选择要处理的字段")
    
    print(f"\n  4. 推荐参数:")
    print(f"     - min_reviews: 0 (电视剧评价数通常较少)")
    print(f"     - min_short_reviews: 10 (保留有一定讨论度的作品)")
    print(f"     - max_workers: 2-5 (避免API限制)")

def main():
    """主函数"""
    print("🎬 开始测试电视剧数据处理...")
    
    success = test_tv_data_processing()
    
    if success:
        print(f"\n🎉 电视剧数据处理测试通过！")
        suggest_tv_usage()
    else:
        print(f"\n❌ 测试失败，请检查上述错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
