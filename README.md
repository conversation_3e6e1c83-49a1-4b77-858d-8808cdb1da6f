# 豆瓣电影数据处理工具 - Web版

这是一个基于Web界面的豆瓣电影数据处理工具，支持使用智谱GLM4.5 API提取电影人员信息。

## 功能特性

- 🎬 **电影数据处理**: 支持导演、编剧、演员信息提取
- 🔧 **多API支持**: 支持智谱GLM4.5和Infini-AI API
- 🌐 **Web界面**: 直观的用户界面，易于操作
- 📊 **实时监控**: 处理进度实时显示
- 🔄 **增量处理**: 支持断点续传和错误重试
- 📈 **结果导出**: 支持CSV、JSON格式导出

## 安装说明

### 1. 环境要求

- Python 3.8+
- pip 包管理器

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动Web应用

```bash
python run_web.py
```

启动后访问: http://localhost:5000

## 使用指南

### 1. 配置API

1. 访问"配置管理"页面
2. 选择API提供商（智谱AI或Infini-AI）
3. 填入API密钥
4. 设置处理参数
5. 保存配置

### 智谱GLM4.5 API配置

- **API提供商**: `zhipu`
- **模型名称**: `glm-4.5`
- **基础URL**: `https://open.bigmodel.cn/api/paas/v4/`
- **获取API密钥**: 访问 [智谱AI开放平台](https://open.bigmodel.cn/)

### Infini-AI API配置

- **API提供商**: `infini`
- **模型名称**: `deepseek-r1`
- **基础URL**: `https://cloud.infini-ai.com/maas/v1/`
- **获取API密钥**: 访问 [Infini-AI平台](https://cloud.infini-ai.com/)

### 2. 上传数据文件

1. 访问"文件上传"页面
2. 选择CSV或PKL格式的数据文件
3. 上传文件

### 数据文件格式要求

**CSV文件示例**:
```csv
ID,biaoti,daoyan,bianjv,zhuyan
1,肖申克的救赎,弗兰克·德拉邦特,弗兰克·德拉邦特,蒂姆·罗宾斯/摩根·弗里曼
2,霸王别姬,陈凯歌,芦苇/李碧华,张国荣/张丰毅/巩俐
```

**字段说明**:
- `ID`: 电影唯一标识符
- `biaoti`: 电影标题
- `daoyan`: 导演姓名（多人用/分隔）
- `bianjv`: 编剧姓名（多人用/分隔）
- `zhuyan`: 主要演员（多人用/分隔）

### 3. 开始处理

1. 选择要处理的字段类型（导演/编剧/演员）
2. 设置过滤条件（最小评价数、短评数等）
3. 点击"开始处理"
4. 等待处理完成

### 4. 查看结果

1. 访问"处理结果"页面
2. 查看生成的结果文件
3. 下载需要的文件

## 配置文件说明

配置文件 `infini_config.json` 包含以下参数：

```json
{
    "api_provider": "zhipu",
    "api_keys": ["您的API密钥"],
    "api_base_url": "https://open.bigmodel.cn/api/paas/v4/",
    "api_model": "glm-4.5",
    "max_workers": 80,
    "batch_size": 800,
    "output_dir": "infini_output",
    "min_reviews": 0,
    "min_short_reviews": 10,
    "retry_refused": false,
    "retry_failed": false,
    "export_only": false,
    "field_to_process": "actor"
}
```

## 目录结构

```
GLM4.5/
├── web_app.py              # Web应用主文件
├── run_web.py              # 启动脚本
├── infini_processor_v2.py  # 核心处理逻辑
├── infini_config.json      # 配置文件
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
├── templates/             # HTML模板目录
│   ├── base.html
│   ├── index.html
│   ├── config.html
│   ├── upload.html
│   ├── process.html
│   └── results.html
├── uploads/               # 上传文件目录
└── infini_output/         # 输出结果目录
```

## 常见问题

### Q: 如何获取智谱GLM4.5 API密钥？

A: 访问 [智谱AI开放平台](https://open.bigmodel.cn/)，注册账号后获取API密钥。

### Q: 处理过程中出现错误怎么办？

A: 系统会自动重试失败的请求。如果仍然失败，请检查：
1. API密钥是否正确
2. 网络连接是否正常
3. API配额是否充足

### Q: 支持哪些文件格式？

A: 目前支持CSV和PKL格式的文件。

### Q: 如何提高处理速度？

A: 可以适当增加 `max_workers` 和 `batch_size` 参数，但要注意API限制。

## 技术架构

- **后端**: Flask + Python异步处理
- **前端**: Bootstrap 5 + jQuery
- **数据处理**: pandas + asyncio
- **API调用**: OpenAI Python客户端

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**注意**: 使用本工具时请遵守相关API的使用条款和隐私政策。