{% extends "base.html" %}

{% block title %}数据处理 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-cogs"></i> 数据处理
        </h2>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-play"></i> 开始处理</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">文件信息</label>
                            <div class="alert alert-info">
                                <strong>文件名:</strong> {{ filename }}<br>
                                <strong>路径:</strong> uploads/{{ filename }}
                            </div>
                        </div>
                        
                        <form id="processForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="data_type" class="form-label">数据类型</label>
                                        <select class="form-select" id="data_type" name="data_type" required>
                                            <option value="movie">电影数据</option>
                                            <option value="book">图书数据</option>
                                            <option value="tv">电视剧数据</option>
                                        </select>
                                        <div class="form-text">选择要处理的数据类型</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="field_to_process" class="form-label">处理字段</label>
                                        <select class="form-select" id="field_to_process" name="field_to_process" required>
                                            <option value="director">导演</option>
                                            <option value="screenwriter">编剧</option>
                                            <option value="actor" selected>演员</option>
                                        </select>
                                        <div class="form-text">选择要处理的字段类型</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="min_reviews" class="form-label">最小评价数</label>
                                        <input type="number" class="form-control" id="min_reviews" name="min_reviews" 
                                               value="0" min="0">
                                        <div class="form-text">电影评价数过滤，0表示不限制</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="min_short_reviews" class="form-label">最小短评数</label>
                                        <input type="number" class="form-control" id="min_short_reviews" name="min_short_reviews" 
                                               value="0" min="0">
                                        <div class="form-text">电影短评数过滤，0表示不限制</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="concurrency" class="form-label">并发数</label>
                                        <div class="d-flex align-items-center gap-3">
                                            <input type="range" class="form-range" id="concurrency" name="concurrency" 
                                                   min="1" max="100" value="30" step="1">
                                            <span class="badge bg-primary" id="concurrencyValue">30</span>
                                        </div>
                                        <div class="form-text">调整并发处理数量 (1-100)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">预估处理时间</label>
                                        <div class="form-control-plaintext">
                                            <span id="estimated-time">计算中...</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">当前配置</label>
                                        <div class="form-control-plaintext">
                                            <small class="text-muted" id="currentConfig">加载中...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
                                    <ul class="mb-0">
                                        <li>处理过程可能需要较长时间，请耐心等待</li>
                                        <li>请确保网络连接稳定</li>
                                        <li>处理过程中不要关闭浏览器</li>
                                        <li>结果将自动保存到配置的输出目录</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary" id="startBtn">
                                    <i class="fas fa-play"></i> 开始处理
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                    <i class="fas fa-arrow-left"></i> 返回
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 处理进度 -->
                <div class="card mt-4" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-tasks"></i> 处理进度</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress-container">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">任务状态</small>
                                    <div id="taskStatus">准备中</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">已处理</small>
                                    <div id="processedCount">0</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">总数量</small>
                                    <div id="totalCount">0</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">用时</small>
                                    <div id="elapsedTime">00:00:00</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert alert-info" id="currentTask">
                                <i class="fas fa-info-circle"></i> 准备开始处理...
                            </div>
                        </div>
                        
                        <!-- 控制按钮 -->
                        <div class="mt-3" id="controlButtons" style="display: none;">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-warning" id="pauseBtn" onclick="pauseProcessing()">
                                    <i class="fas fa-pause"></i> 暂停
                                </button>
                                <button type="button" class="btn btn-success" id="resumeBtn" onclick="resumeProcessing()" style="display: none;">
                                    <i class="fas fa-play"></i> 继续
                                </button>
                                <button type="button" class="btn btn-danger" id="stopBtn" onclick="stopProcessing()">
                                    <i class="fas fa-stop"></i> 停止
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 断点续传信息卡片 -->
                <div class="card mt-4" id="checkpointCard" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-bookmark"></i> 断点续传信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="checkpoint-info-container">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">数据类型</small>
                                    <div id="checkpointDataType" class="fw-bold">-</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">处理字段</small>
                                    <div id="checkpointFieldType" class="fw-bold">-</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">处理进度</small>
                                    <span id="checkpointProgress" class="badge bg-primary">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="checkpointProgressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-4">
                                    <small class="text-muted">已处理</small>
                                    <div id="checkpointProcessed" class="fw-bold text-success">0</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">总数量</small>
                                    <div id="checkpointTotal" class="fw-bold">0</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">成功率</small>
                                    <div id="checkpointSuccessRate" class="fw-bold text-info">0%</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">最后更新</small>
                                <div id="checkpointLastUpdate" class="small">-</div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success btn-sm" id="resumeCheckpointBtn" onclick="resumeFromCheckpoint()">
                                    <i class="fas fa-play"></i> 从断点继续
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" onclick="resetCheckpoint()">
                                    <i class="fas fa-redo"></i> 重置断点
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 断点信息侧边栏 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-bookmark"></i> 断点信息</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="showCheckpointDetails()">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="checkpointInfo">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list"></i> 处理队列</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="showTaskQueueManage()">
                            <i class="fas fa-cogs"></i> 管理
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="taskQueue">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> 实时统计</h5>
                    </div>
                    <div class="card-body">
                        <div id="realTimeStats">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 断点详情弹窗 -->
<div class="modal fade" id="checkpointModal" tabindex="-1" aria-labelledby="checkpointModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="checkpointModalLabel">
                    <i class="fas fa-bookmark"></i> 断点详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="checkpointModalBody">
                <!-- 断点详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshCheckpoint()">刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 断点管理弹窗 -->
<div class="modal fade" id="checkpointManageModal" tabindex="-1" aria-labelledby="checkpointManageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="checkpointManageModalLabel">
                    <i class="fas fa-cogs"></i> 断点管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="checkpointManageModalBody">
                <!-- 断点管理内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshCheckpointList()">刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 处理队列管理弹窗 -->
<div class="modal fade" id="taskQueueManageModal" tabindex="-1" aria-labelledby="taskQueueManageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskQueueManageModalLabel">
                    <i class="fas fa-tasks"></i> 处理队列管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="taskQueueManageModalBody">
                <!-- 处理队列管理内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshTaskQueue()">刷新</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentTaskId = null;
let progressInterval = null;

$(document).ready(function() {
    console.log('document.ready: 页面加载完成，开始初始化');
    console.log('document.ready: jQuery版本', $().jquery);

    loadTaskQueue();
    loadRealTimeStats();
    loadCurrentConfig();
    updateFieldOptions('movie'); // 初始化字段选项
    loadCheckpointInfo();

    console.log('document.ready: 初始化函数调用完成');

    // 数据类型切换事件
    $('#data_type').on('change', function() {
        const dataType = $(this).val();
        console.log('数据类型切换:', dataType);
        updateFieldOptions(dataType);
    });

    // 字段类型切换事件
    $('#field_to_process').on('change', function() {
        console.log('字段类型切换:', $(this).val());
        updateConfigDisplay();
        loadCheckpointInfo();
    });

    // 并发数滑块事件
    $('#concurrency').on('input', function() {
        $('#concurrencyValue').text($(this).val());
        updateConfigDisplay();
    });
    
    // 处理字段选择事件
    $('#field_to_process').on('change', function() {
        updateConfigDisplay();
        loadCheckpointInfo();
    });
    
    // 表单提交
    $('#processForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            filename: '{{ filename }}',
            data_type: formData.get('data_type'),  // 新增数据类型参数
            field_to_process: formData.get('field_to_process'),
            min_reviews: parseInt(formData.get('min_reviews')),
            min_short_reviews: parseInt(formData.get('min_short_reviews')),
            concurrency: parseInt(formData.get('concurrency'))
        };
        
        // 禁用开始按钮
        $('#startBtn').prop('disabled', true);
        $('#startBtn').html('<i class="fas fa-spinner fa-spin"></i> 启动中...');
        
        // 发送处理请求
        $.ajax({
            url: '/api/start_processing',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(response) {
                if (response.success) {
                    currentTaskId = response.task_id;
                    showProgressCard();
                    startProgressMonitoring();
                } else {
                    alert('启动失败：' + response.message);
                    $('#startBtn').prop('disabled', false);
                    $('#startBtn').html('<i class="fas fa-play"></i> 开始处理');
                }
            },
            error: function() {
                alert('网络错误，请稍后重试');
                $('#startBtn').prop('disabled', false);
                $('#startBtn').html('<i class="fas fa-play"></i> 开始处理');
            }
        });
    });
    
    // 定时刷新任务队列和统计
    setInterval(function() {
        loadTaskQueue();
        loadRealTimeStats();
    }, 3000);
    
    // 定时刷新断点信息
    setInterval(function() {
        loadCheckpointInfo();
    }, 5000);
});

function showProgressCard() {
    $('#progressCard').show();
    $('#controlButtons').show();
    $('#taskStatus').text('处理中');
    $('#currentTask').html('<i class="fas fa-spinner fa-spin"></i> 正在初始化处理任务...');
}

function startProgressMonitoring() {
    // 清除之前的定时器
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    const startTime = Date.now();
    
    progressInterval = setInterval(function() {
        if (!currentTaskId) return;
        
        $.get('/api/task_status/' + currentTaskId, function(response) {
            if (response.success) {
                updateProgress(response, startTime);
                
                // 如果任务完成、出错、停止，停止监控
                if (response.status === 'completed' || response.status === 'error' || response.status === 'stopped') {
                    clearInterval(progressInterval);
                    
                    if (response.status === 'completed') {
                        $('#taskStatus').text('已完成');
                        $('#currentTask').html('<i class="fas fa-check-circle text-success"></i> 处理完成！');
                        $('#progressBar').removeClass('progress-bar-animated');
                        $('#progressBar').addClass('bg-success');
                    } else if (response.status === 'stopped') {
                        $('#taskStatus').text('已停止');
                        $('#currentTask').html('<i class="fas fa-stop text-danger"></i> 处理已停止');
                        $('#progressBar').removeClass('progress-bar-animated');
                        $('#progressBar').addClass('bg-danger');
                    } else {
                        $('#taskStatus').text('出错');
                        $('#currentTask').html('<i class="fas fa-exclamation-circle text-danger"></i> 处理出错：' + response.error);
                        $('#progressBar').addClass('bg-danger');
                    }
                    
                    // 重新启用开始按钮
                    $('#startBtn').prop('disabled', false);
                    $('#startBtn').html('<i class="fas fa-play"></i> 开始处理');
                }
            }
        });
    }, 1000);
}

function updateProgress(taskData, startTime) {
    // 更新用时
    const elapsed = (Date.now() - startTime) / 1000;
    const hours = Math.floor(elapsed / 3600);
    const minutes = Math.floor((elapsed % 3600) / 60);
    const seconds = Math.floor(elapsed % 60);
    $('#elapsedTime').text(
        String(hours).padStart(2, '0') + ':' +
        String(minutes).padStart(2, '0') + ':' +
        String(seconds).padStart(2, '0')
    );
    
    // 更新进度条（使用实际进度数据）
    let progress = 0;
    let processedText = '0';
    let totalText = '0';
    let statusText = '准备中';
    
    if (taskData.progress) {
        const progressData = taskData.progress;
        processedText = progressData.processed_count || 0;
        totalText = progressData.total_count || 0;
        
        if (totalText > 0) {
            progress = (processedText / totalText) * 100;
        }
        
        // 更新详细进度信息
        $('#processedCount').text(processedText);
        $('#totalCount').text(totalText);
        
        // 更新成功率
        const successRate = progressData.success_rate || 0;
        const errorCount = progressData.error_count || 0;
        
        // 计算处理速度和预计剩余时间
        let speedText = '';
        let etaText = '';
        if (elapsed > 0 && processedText > 0) {
            const speed = processedText / elapsed; // 每秒处理数
            speedText = `速度: ${speed.toFixed(2)} 项/秒`;
            
            if (speed > 0 && totalText > processedText) {
                const remainingItems = totalText - processedText;
                const remainingSeconds = remainingItems / speed;
                const etaHours = Math.floor(remainingSeconds / 3600);
                const etaMinutes = Math.floor((remainingSeconds % 3600) / 60);
                etaText = `预计剩余: ${etaHours}时${etaMinutes}分`;
            }
        }
        
        // 更新控制按钮状态
        updateControlButtons(taskData.status, progressData);
        
        // 根据状态更新显示
        if (taskData.status === 'paused') {
            statusText = `已暂停: ${processedText}/${totalText} (${progress.toFixed(1)}%)`;
            $('#taskStatus').text('已暂停');
            $('#progressBar').removeClass('progress-bar-animated');
            $('#currentTask').html('<i class="fas fa-pause text-warning"></i> 处理已暂停');
        } else if (taskData.status === 'stopped') {
            statusText = `已停止: ${processedText}/${totalText} (${progress.toFixed(1)}%)`;
            $('#taskStatus').text('已停止');
            $('#progressBar').removeClass('progress-bar-animated');
            $('#currentTask').html('<i class="fas fa-stop text-danger"></i> 处理已停止');
        } else if (taskData.status === 'running') {
            statusText = `处理中: ${processedText}/${totalText} (${progress.toFixed(1)}%)`;
            $('#taskStatus').text('处理中');
            $('#progressBar').addClass('progress-bar-animated');
            
            if (progressData.current_item) {
                $('#currentTask').html(`<i class="fas fa-spinner fa-spin"></i> 正在处理: ${progressData.current_item}`);
            } else {
                $('#currentTask').html(`<i class="fas fa-spinner fa-spin"></i> ${statusText}`);
            }
        }
        
        // 构建详细状态信息
        let detailHtml = '';
        if (speedText) {
            detailHtml += `<div><small class="text-muted">${speedText}</small></div>`;
        }
        if (etaText) {
            detailHtml += `<div><small class="text-muted">${etaText}</small></div>`;
        }
        if (errorCount > 0) {
            detailHtml += `<div><small class="text-danger">错误: ${errorCount}</small></div>`;
        }
        if (successRate > 0) {
            detailHtml += `<div><small class="text-success">成功率: ${successRate.toFixed(1)}%</small></div>`;
        }
        
        if (detailHtml) {
            $('#currentTask').html(`
                <div>
                    ${$('#currentTask').html()}
                    <div class="mt-2">
                        ${detailHtml}
                    </div>
                </div>
            `);
        }
    }
    
    $('#progressBar').css('width', progress + '%');
    $('#progressBar').text(Math.round(progress) + '%');
}

function loadTaskQueue() {
    console.log('loadTaskQueue: 开始加载任务队列');
    $.get('/api/tasks', function(data) {
        console.log('loadTaskQueue: API响应成功', data);
        if (data.success) {
            let html = '';
            if (data.tasks.length === 0) {
                html = '<small class="text-muted">暂无处理任务</small>';
                console.log('loadTaskQueue: 没有任务');
            } else {
                console.log('loadTaskQueue: 有 ' + data.tasks.length + ' 个任务');
                data.tasks.forEach(function(task) {
                    console.log('loadTaskQueue: 处理任务', task);
                    let statusClass = task.status === 'running' ? 'status-running' : 
                                     task.status === 'completed' ? 'status-completed' : 'status-error';
                    let statusIcon = task.status === 'running' ? 'fa-spinner fa-spin' : 
                                     task.status === 'completed' ? 'fa-check-circle' : 'fa-exclamation-circle';
                    
                    // 添加进度显示
                    let progressHtml = '';
                    if (task.status === 'running' && task.progress !== undefined) {
                        const progressPercent = Math.round(task.progress);
                        progressHtml = `
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-success" style="width: ${progressPercent}%"></div>
                            </div>
                            <small class="text-muted">${progressPercent}% - ${task.message || '正在处理...'}</small>
                        `;
                    } else if (task.status === 'completed') {
                        progressHtml = `<small class="text-success">已完成 - ${task.message || '处理完成'}</small>`;
                    } else if (task.status === 'error') {
                        progressHtml = `<small class="text-danger">错误: ${task.error || '未知错误'}</small>`;
                    } else if (task.status === 'running') {
                        progressHtml = `<small class="text-info">正在运行... - ${task.message || '正在处理'}</small>`;
                    }
                    
                    html += `
                        <div class="mb-2 p-2 border rounded">
                            <div class="d-flex justify-content-between">
                                <small class="fw-bold">${task.filename}</small>
                                <i class="fas ${statusIcon} ${statusClass}"></i>
                            </div>
                            <small class="text-muted">${new Date(task.start_time).toLocaleString()}</small>
                            ${progressHtml}
                        </div>
                    `;
                });
            }
            console.log('loadTaskQueue: 更新HTML');
            $('#taskQueue').html(html);
            console.log('loadTaskQueue: HTML更新完成');
        } else {
            console.log('loadTaskQueue: API响应失败', data.message);
            $('#taskQueue').html('<small class="text-danger">加载失败</small>');
        }
    }).fail(function(xhr, status, error) {
        console.error('loadTaskQueue: API调用失败', error);
        $('#taskQueue').html('<small class="text-danger">连接失败</small>');
    });
}

function loadRealTimeStats() {
    // 模拟实时统计
    const stats = {
        running_tasks: 2,
        completed_tasks: 15,
        total_processed: 1234,
        success_rate: 95.6
    };
    
    const html = `
        <div class="row text-center">
            <div class="col-6 mb-3">
                <div class="border rounded p-2">
                    <div class="h5 mb-0 text-primary">${stats.running_tasks}</div>
                    <small class="text-muted">运行中</small>
                </div>
            </div>
            <div class="col-6 mb-3">
                <div class="border rounded p-2">
                    <div class="h5 mb-0 text-success">${stats.completed_tasks}</div>
                    <small class="text-muted">已完成</small>
                </div>
            </div>
            <div class="col-6">
                <div class="border rounded p-2">
                    <div class="h5 mb-0">${stats.total_processed}</div>
                    <small class="text-muted">已处理</small>
                </div>
            </div>
            <div class="col-6">
                <div class="border rounded p-2">
                    <div class="h5 mb-0">${stats.success_rate}%</div>
                    <small class="text-muted">成功率</small>
                </div>
            </div>
        </div>
    `;
    $('#realTimeStats').html(html);
}

function loadCurrentConfig() {
    $.get('/api/config', function(data) {
        if (data) {
            $('#concurrency').val(data.max_workers || 30);
            $('#concurrencyValue').text(data.max_workers || 30);
            updateConfigDisplay();
        }
    });
}

function updateFieldOptions(dataType) {
    const fieldMappings = {
        'movie': [
            { value: 'director', text: '导演' },
            { value: 'screenwriter', text: '编剧' },
            { value: 'actor', text: '演员' }
        ],
        'book': [
            { value: 'author', text: '作者' },
            { value: 'translator', text: '译者' },
            { value: 'editor', text: '编辑' }
        ],
        'tv': [
            { value: 'director', text: '导演' },
            { value: 'writer', text: '编剧' },
            { value: 'cast', text: '演员' }
        ]
    };

    const fields = fieldMappings[dataType] || fieldMappings['movie'];
    const fieldSelect = $('#field_to_process');
    const currentValue = fieldSelect.val();

    fieldSelect.empty();
    fields.forEach(field => {
        fieldSelect.append(`<option value="${field.value}">${field.text}</option>`);
    });

    // 尝试保持当前选择，如果不存在则选择第一个
    if (fields.some(f => f.value === currentValue)) {
        fieldSelect.val(currentValue);
    } else {
        fieldSelect.val(fields[0].value);
    }

    updateConfigDisplay();
    loadCheckpointInfo();
}

function updateConfigDisplay() {
    const dataType = $('#data_type').val();
    const field = $('#field_to_process').val();
    const concurrency = $('#concurrency').val();

    const dataTypeNames = {
        'movie': '电影',
        'book': '图书',
        'tv': '电视剧'
    };

    const fieldNames = {
        'director': '导演',
        'screenwriter': '编剧',
        'actor': '演员',
        'author': '作者',
        'translator': '译者',
        'editor': '编辑',
        'writer': '编剧',
        'cast': '演员'
    };

    const configText = `数据类型: ${dataTypeNames[dataType]} | 处理字段: ${fieldNames[field]} | 并发数: ${concurrency}`;
    $('#currentConfig').text(configText);
}

function loadCheckpointInfo() {
    console.log('loadCheckpointInfo: 开始加载断点信息');
    const dataType = $('#data_type').val();
    const field = $('#field_to_process').val();
    console.log('loadCheckpointInfo: 当前数据类型和字段', dataType, field);

    // 优先使用新的API端点，如果失败则回退到旧的
    $.get(`/api/checkpoint/${dataType}/${field}`)
        .done(function(data) {
            console.log('loadCheckpointInfo: 新API响应成功', data);
            handleCheckpointData(data);
        })
        .fail(function() {
            console.log('loadCheckpointInfo: 新API失败，尝试旧API');
            $.get('/api/checkpoint/' + field, function(data) {
                console.log('loadCheckpointInfo: 旧API响应成功', data);
                handleCheckpointData(data);
            });
        });
}

function handleCheckpointData(data) {
        if (data.success) {
            const checkpoint = data.checkpoint;
            const processedIds = checkpoint.processed_ids || [];
            const totalCount = checkpoint.total_ids || 0;
            const processedCount = processedIds.length;
            const remainingCount = totalCount - processedCount;
            const progressPercent = totalCount > 0 ? (processedCount / totalCount * 100).toFixed(1) : 0;

            // 更新新的断点信息卡片
            updateCheckpointCard(checkpoint, processedCount, totalCount, progressPercent);
            
            const fieldNames = {
                'director': '导演',
                'screenwriter': '编剧',
                'actor': '演员'
            };
            console.log('loadCheckpointInfo: 断点数据', {
                processedCount, totalCount, progressPercent, field
            });
            
            const html = `
                <div class="checkpoint-status">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">${fieldNames[field]}</span>
                        <small class="text-muted">${new Date(checkpoint.modified).toLocaleString()}</small>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: ${progressPercent}%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h6 mb-0">${processedCount}</div>
                            <small class="text-muted">已处理</small>
                        </div>
                        <div class="col-4">
                            <div class="h6 mb-0">${remainingCount}</div>
                            <small class="text-muted">剩余</small>
                        </div>
                        <div class="col-4">
                            <div class="h6 mb-0">${progressPercent}%</div>
                            <small class="text-muted">完成率</small>
                        </div>
                    </div>
                    ${checkpoint.last_processed_index ? `
                        <div class="mt-2 text-center">
                            <small class="text-muted">最后处理: 第${checkpoint.last_processed_index}项</small>
                        </div>
                    ` : ''}
                </div>
            `;
            $('#checkpointInfo').html(html);
        } else {
            $('#checkpointInfo').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle"></i>
                    <small>暂无断点信息</small>
                </div>
            `);
            // 隐藏断点卡片
            $('#checkpointCard').hide();
        }
    }).fail(function() {
        $('#checkpointInfo').html(`
            <div class="text-center text-muted">
                <i class="fas fa-exclamation-triangle"></i>
                <small>加载失败</small>
            </div>
        `);
    });
}

function updateCheckpointCard(checkpoint, processedCount, totalCount, progressPercent) {
    const dataType = $('#data_type').val();
    const field = $('#field_to_process').val();

    const dataTypeNames = {
        'movie': '电影',
        'book': '图书',
        'tv': '电视剧'
    };

    const fieldNames = {
        'director': '导演',
        'screenwriter': '编剧',
        'actor': '演员',
        'author': '作者',
        'translator': '译者',
        'editor': '编辑',
        'writer': '编剧',
        'cast': '演员'
    };

    // 更新数据类型和字段类型
    $('#checkpointDataType').text(dataTypeNames[dataType] || dataType);
    $('#checkpointFieldType').text(fieldNames[field] || field);

    // 更新进度信息
    $('#checkpointProgress').text(`${progressPercent}%`);
    $('#checkpointProgressBar').css('width', `${progressPercent}%`);

    // 更新统计信息
    $('#checkpointProcessed').text(processedCount);
    $('#checkpointTotal').text(totalCount);

    // 计算成功率（如果有相关数据）
    const successRate = checkpoint.success_count && totalCount > 0 ?
        Math.round((checkpoint.success_count / totalCount) * 100) : progressPercent;
    $('#checkpointSuccessRate').text(`${successRate}%`);

    // 更新最后更新时间
    if (checkpoint.modified) {
        const modifiedDate = new Date(checkpoint.modified);
        $('#checkpointLastUpdate').text(modifiedDate.toLocaleString());
    }

    // 显示断点卡片
    $('#checkpointCard').show();
}

function resumeFromCheckpoint() {
    const dataType = $('#data_type').val();
    const field = $('#field_to_process').val();

    if (confirm(`确定要从断点继续处理 ${dataType} 数据的 ${field} 字段吗？`)) {
        // 这里可以添加从断点继续的逻辑
        // 例如：自动填充表单并开始处理
        $('#field_to_process').val(field);
        $('#data_type').val(dataType);

        showAlert('info', '正在从断点继续处理...');

        // 可以触发处理表单提交
        // $('#processForm').submit();
    }
}

function resetCheckpoint() {
    const dataType = $('#data_type').val();
    const field = $('#field_to_process').val();

    if (confirm(`确定要重置 ${dataType} 数据的 ${field} 字段的断点吗？此操作不可撤销。`)) {
        $.post(`/api/checkpoint/${dataType}/${field}/reset`)
            .done(function(data) {
                if (data.success) {
                    showAlert('success', data.message);
                    loadCheckpointInfo(); // 重新加载断点信息
                } else {
                    showAlert('danger', data.message);
                }
            })
            .fail(function() {
                // 尝试旧API
                $.post(`/api/checkpoint/${field}/reset`)
                    .done(function(data) {
                        if (data.success) {
                            showAlert('success', data.message);
                            loadCheckpointInfo();
                        } else {
                            showAlert('danger', data.message);
                        }
                    })
                    .fail(function() {
                        showAlert('danger', '重置断点失败');
                    });
            });
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').first().prepend(alertHtml);

    // 自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

function showCheckpointDetails() {
    const field = $('#field_to_process').val();
    $.get('/api/checkpoint/' + field, function(data) {
        if (data.success) {
            const checkpoint = data.checkpoint;
            const processedIds = checkpoint.processed_ids || [];
            const totalCount = checkpoint.total_ids || 0;
            const processedCount = processedIds.length;
            const remainingCount = totalCount - processedCount;
            const progressPercent = totalCount > 0 ? (processedCount / totalCount * 100).toFixed(1) : 0;
            
            const fieldNames = {
                'director': '导演',
                'screenwriter': '编剧',
                'actor': '演员'
            };
            
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>字段类型</td>
                                <td>${fieldNames[field]}</td>
                            </tr>
                            <tr>
                                <td>文件路径</td>
                                <td><code>${checkpoint.filename}</code></td>
                            </tr>
                            <tr>
                                <td>文件大小</td>
                                <td>${(checkpoint.size / 1024).toFixed(2)} KB</td>
                            </tr>
                            <tr>
                                <td>创建时间</td>
                                <td>${new Date(checkpoint.created).toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td>修改时间</td>
                                <td>${new Date(checkpoint.modified).toLocaleString()}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>处理统计</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" style="width: ${progressPercent}%">${progressPercent}%</div>
                        </div>
                        <table class="table table-sm">
                            <tr>
                                <td>总数量</td>
                                <td>${totalCount}</td>
                            </tr>
                            <tr>
                                <td>已处理</td>
                                <td>${processedCount}</td>
                            </tr>
                            <tr>
                                <td>剩余</td>
                                <td>${remainingCount}</td>
                            </tr>
                            <tr>
                                <td>完成率</td>
                                <td>${progressPercent}%</td>
                            </tr>
                        </table>
                    </div>
                </div>
                ${checkpoint.last_processed_index ? `
                    <div class="mt-3">
                        <h6>处理进度</h6>
                        <p class="text-muted">最后处理到第 <strong>${checkpoint.last_processed_index}</strong> 项</p>
                    </div>
                ` : ''}
                <div class="mt-3">
                    <h6>操作</h6>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="exportCheckpoint('${field}')">
                            <i class="fas fa-download"></i> 导出断点
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="resetCheckpoint('${field}')">
                            <i class="fas fa-redo"></i> 重置断点
                        </button>
                    </div>
                </div>
            `;
            $('#checkpointModalBody').html(html);
            $('#checkpointModal').modal('show');
        } else {
            alert('加载断点详情失败: ' + data.message);
        }
    });
}

function showCheckpointManage() {
    $.get('/api/checkpoints', function(data) {
        if (data.success) {
            const checkpoints = data.checkpoints;
            let html = '';
            
            if (checkpoints.length === 0) {
                html = `
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无断点文件</p>
                    </div>
                `;
            } else {
                html = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>字段类型</th>
                                    <th>文件名</th>
                                    <th>大小</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                checkpoints.forEach(function(checkpoint) {
                    const fieldNames = {
                        'director': '导演',
                        'screenwriter': '编剧',
                        'actor': '演员'
                    };
                    
                    const processedCount = (checkpoint.processed_ids || []).length;
                    const totalCount = checkpoint.total_ids || 0;
                    const progressPercent = totalCount > 0 ? (processedCount / totalCount * 100).toFixed(1) : 0;
                    
                    html += `
                        <tr>
                            <td>
                                <span class="badge bg-primary">${fieldNames[checkpoint.field_type]}</span>
                            </td>
                            <td><code>${checkpoint.filename}</code></td>
                            <td>${(checkpoint.size / 1024).toFixed(2)} KB</td>
                            <td>${new Date(checkpoint.created).toLocaleString()}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" onclick="exportCheckpoint('${checkpoint.field_type}')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="resetCheckpoint('${checkpoint.field_type}')">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            $('#checkpointManageModalBody').html(html);
            $('#checkpointManageModal').modal('show');
        } else {
            alert('加载断点管理失败: ' + data.message);
        }
    });
}

function refreshCheckpoint() {
    loadCheckpointInfo();
    showCheckpointDetails();
}

function refreshCheckpointList() {
    showCheckpointManage();
}

function showTaskQueueManage() {
    $.get('/api/tasks', function(data) {
        if (data.success) {
            const tasks = data.tasks;
            let html = '';
            
            if (tasks.length === 0) {
                html = `
                    <div class="text-center py-4">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无处理任务</p>
                    </div>
                `;
            } else {
                html = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>文件名</th>
                                    <th>状态</th>
                                    <th>开始时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                tasks.forEach(function(task) {
                    const statusClass = task.status === 'running' ? 'status-running' : 
                                     task.status === 'completed' ? 'status-completed' : 'status-error';
                    const statusBadge = task.status === 'running' ? 'primary' : 
                                     task.status === 'completed' ? 'success' : 'danger';
                    const statusText = task.status === 'running' ? '运行中' : 
                                     task.status === 'completed' ? '已完成' : '错误';
                    
                    html += `
                        <tr>
                            <td><code>${task.task_id}</code></td>
                            <td>${task.filename}</td>
                            <td><span class="badge bg-${statusBadge}">${statusText}</span></td>
                            <td>${new Date(task.start_time).toLocaleString()}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    ${task.status === 'running' ? `
                                        <button type="button" class="btn btn-outline-warning" onclick="pauseTask('${task.task_id}')">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="stopTask('${task.task_id}')">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    ` : ''}
                                    <button type="button" class="btn btn-outline-info" onclick="viewTaskDetails('${task.task_id}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            $('#taskQueueManageModalBody').html(html);
            $('#taskQueueManageModal').modal('show');
        } else {
            alert('加载处理队列失败: ' + data.message);
        }
    });
}

function refreshTaskQueue() {
    showTaskQueueManage();
    loadTaskQueue(); // 同时更新侧边栏
}

function pauseTask(taskId) {
    $.post('/api/pause_processing/' + taskId, function(data) {
        if (data.success) {
            alert('任务已暂停');
            refreshTaskQueue();
        } else {
            alert('暂停失败: ' + data.message);
        }
    });
}

function stopTask(taskId) {
    if (confirm('确定要停止这个任务吗？停止后将无法继续。')) {
        $.post('/api/stop_processing/' + taskId, function(data) {
            if (data.success) {
                alert('任务已停止');
                refreshTaskQueue();
            } else {
                alert('停止失败: ' + data.message);
            }
        });
    }
}

function viewTaskDetails(taskId) {
    $.get('/api/task_status/' + taskId, function(data) {
        if (data.success) {
            const task = data;
            const progress = task.progress || {};
            
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>任务信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>任务ID</td>
                                <td><code>${taskId}</code></td>
                            </tr>
                            <tr>
                                <td>文件名</td>
                                <td>${task.filename}</td>
                            </tr>
                            <tr>
                                <td>状态</td>
                                <td><span class="badge bg-primary">${task.status}</span></td>
                            </tr>
                            <tr>
                                <td>开始时间</td>
                                <td>${new Date(task.start_time).toLocaleString()}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>处理进度</h6>
                        ${progress.processed_count !== undefined ? `
                            <div class="progress mb-2">
                                <div class="progress-bar" style="width: ${((progress.processed_count / progress.total_count) * 100).toFixed(1)}%">
                                    ${((progress.processed_count / progress.total_count) * 100).toFixed(1)}%
                                </div>
                            </div>
                            <table class="table table-sm">
                                <tr>
                                    <td>已处理</td>
                                    <td>${progress.processed_count || 0}</td>
                                </tr>
                                <tr>
                                    <td>总数量</td>
                                    <td>${progress.total_count || 0}</td>
                                </tr>
                                <tr>
                                    <td>成功率</td>
                                    <td>${(progress.success_rate || 0).toFixed(1)}%</td>
                                </tr>
                            </table>
                        ` : '<p class="text-muted">暂无进度信息</p>'}
                    </div>
                </div>
            `;
            
            // 创建临时弹窗显示任务详情
            const modal = $(`
                <div class="modal fade" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">任务详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">${html}</div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(modal);
            modal.modal('show');
            modal.on('hidden.bs.modal', function() {
                modal.remove();
            });
        } else {
            alert('加载任务详情失败: ' + data.message);
        }
    });
}

function exportCheckpoint(field) {
    $.get('/api/checkpoint/' + field + '/export', function(data) {
        if (data.success) {
            alert('断点导出成功！\n文件: ' + data.export_file);
        } else {
            alert('导出失败: ' + data.message);
        }
    });
}

function resetCheckpoint(field) {
    if (confirm('确定要重置断点吗？此操作不可恢复。')) {
        $.post('/api/checkpoint/' + field + '/reset', function(data) {
            if (data.success) {
                alert('断点重置成功');
                loadCheckpointInfo();
                if ($('#checkpointModal').is(':visible')) {
                    $('#checkpointModal').modal('hide');
                }
                if ($('#checkpointManageModal').is(':visible')) {
                    refreshCheckpointList();
                }
            } else {
                alert('重置失败: ' + data.message);
            }
        });
    }
}

function updateControlButtons(status, progressData) {
    const isPaused = progressData.is_paused || false;
    const shouldStop = progressData.should_stop || false;
    
    if (status === 'paused' || isPaused) {
        $('#pauseBtn').hide();
        $('#resumeBtn').show();
        $('#stopBtn').show();
    } else if (status === 'stopped' || shouldStop) {
        $('#pauseBtn').hide();
        $('#resumeBtn').hide();
        $('#stopBtn').hide();
    } else if (status === 'running') {
        $('#pauseBtn').show();
        $('#resumeBtn').hide();
        $('#stopBtn').show();
    } else {
        // completed or error
        $('#pauseBtn').hide();
        $('#resumeBtn').hide();
        $('#stopBtn').hide();
    }
}

function pauseProcessing() {
    if (!currentTaskId) return;
    
    $('#pauseBtn').prop('disabled', true);
    $('#pauseBtn').html('<i class="fas fa-spinner fa-spin"></i> 暂停中...');
    
    $.ajax({
        url: '/api/pause_processing/' + currentTaskId,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                alert('处理已暂停');
            } else {
                alert('暂停失败：' + response.message);
            }
            $('#pauseBtn').prop('disabled', false);
            $('#pauseBtn').html('<i class="fas fa-pause"></i> 暂停');
        },
        error: function() {
            alert('网络错误，请稍后重试');
            $('#pauseBtn').prop('disabled', false);
            $('#pauseBtn').html('<i class="fas fa-pause"></i> 暂停');
        }
    });
}

function resumeProcessing() {
    if (!currentTaskId) return;
    
    $('#resumeBtn').prop('disabled', true);
    $('#resumeBtn').html('<i class="fas fa-spinner fa-spin"></i> 继续中...');
    
    $.ajax({
        url: '/api/resume_processing/' + currentTaskId,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                alert('处理已继续');
            } else {
                alert('继续失败：' + response.message);
            }
            $('#resumeBtn').prop('disabled', false);
            $('#resumeBtn').html('<i class="fas fa-play"></i> 继续');
        },
        error: function() {
            alert('网络错误，请稍后重试');
            $('#resumeBtn').prop('disabled', false);
            $('#resumeBtn').html('<i class="fas fa-play"></i> 继续');
        }
    });
}

function stopProcessing() {
    if (!currentTaskId) return;
    
    if (!confirm('确定要停止处理吗？停止后将无法继续。')) {
        return;
    }
    
    $('#stopBtn').prop('disabled', true);
    $('#stopBtn').html('<i class="fas fa-spinner fa-spin"></i> 停止中...');
    
    $.ajax({
        url: '/api/stop_processing/' + currentTaskId,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                alert('处理已停止');
                clearInterval(progressInterval);
            } else {
                alert('停止失败：' + response.message);
            }
            $('#stopBtn').prop('disabled', false);
            $('#stopBtn').html('<i class="fas fa-stop"></i> 停止');
        },
        error: function() {
            alert('网络错误，请稍后重试');
            $('#stopBtn').prop('disabled', false);
            $('#stopBtn').html('<i class="fas fa-stop"></i> 停止');
        }
    });
}
</script>
{% endblock %}