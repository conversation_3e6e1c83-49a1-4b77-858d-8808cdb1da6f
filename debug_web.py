#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试Web应用问题
"""
import os
import sys
from web_app import app

def test_routes():
    """测试所有路由"""
    print("测试Web应用路由...")
    
    with app.test_client() as client:
        routes = [
            ('/', 'GET'),
            ('/config', 'GET'),
            ('/api/config', 'GET'),
            ('/api/tasks', 'GET'),
            ('/api/results', 'GET'),
            ('/upload', 'GET'),
            ('/results', 'GET')
        ]
        
        for route, method in routes:
            print(f"\n测试 {method} {route}")
            try:
                if method == 'GET':
                    response = client.get(route)
                elif method == 'POST':
                    response = client.post(route)
                
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 成功")
                    if route.startswith('/api/'):
                        try:
                            data = response.get_json()
                            print(f"响应数据: {data}")
                        except:
                            print("响应不是JSON格式")
                else:
                    print(f"❌ 失败: {response.get_data(as_text=True)}")
                    
            except Exception as e:
                print(f"❌ 异常: {e}")

def check_files():
    """检查必要文件"""
    print("\n检查必要文件...")
    
    files = [
        'infini_config.json',
        'templates/base.html',
        'templates/index.html',
        'templates/config.html',
        'templates/upload.html',
        'templates/process.html',
        'templates/results.html'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")

def main():
    """主函数"""
    print("开始调试Web应用...\n")
    
    check_files()
    test_routes()
    
    print("\n调试完成。")

if __name__ == "__main__":
    main()