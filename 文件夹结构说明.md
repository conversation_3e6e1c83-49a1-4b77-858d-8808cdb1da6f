# 文件夹结构说明

## 目录结构
```
GLM4.5/
├── logs/                 # 日志文件目录
│   ├── infini_processing_*.log    # 处理器日志
│   ├── infini_console_*.log        # 控制台日志
│   └── web_app.log                 # Web应用日志
├── dataset/              # 数据集目录
│   └── all_data.pkl              # 豆瓣电影数据集
├── uploads/              # 文件上传目录
│   └── *.csv, *.pkl             # 用户上传的数据文件
├── infini_output/        # 处理结果输出目录
│   ├── *.json                     # 处理结果和检查点文件
│   └── *.csv                      # 导出的结果文件
├── templates/            # Web模板目录
│   ├── base.html                 # 基础模板
│   ├── index.html                # 首页
│   ├── config.html               # 配置页面
│   ├── upload.html               # 上传页面
│   ├── process.html              # 处理页面
│   └── results.html              # 结果页面
├── web_app.py            # Flask Web应用主文件
├── infini_processor_v2.py # 核心处理逻辑
├── run_web.py            # Web应用启动脚本
├── infini_config.json    # 配置文件
├── requirements.txt     # Python依赖
└── .gitignore           # Git忽略文件
```

## 日志文件管理
- 所有日志文件自动保存到 `logs/` 目录
- 日志文件按时间戳命名，格式：`YYYYMMDD_HHMMSS`
- 包含处理器日志、控制台日志和Web应用日志
- 通过 `.gitignore` 文件防止日志文件被版本控制

## 使用说明
1. 启动应用：`python run_web.py`
2. 访问：http://localhost:5000
3. 日志会自动保存到 `logs/` 目录
4. 处理结果保存在 `infini_output/` 目录
5. 上传的文件保存在 `uploads/` 目录