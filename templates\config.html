{% extends "base.html" %}

{% block title %}配置管理 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <h2>
            <i class="fas fa-cogs me-2"></i> 配置管理
        </h2>
        <p class="text-muted">管理API配置和系统参数</p>
    </div>
</div>

<div class="row">
    <!-- 配置表单 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-key"></i> API配置</h5>
            </div>
            <div class="card-body">
                <form id="configForm">
                    <!-- API提供商选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">API提供商</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="api_provider" id="provider_infini" value="infini" {% if config.get('api_provider') == 'infini' %}checked{% endif %}>
                                <label class="btn btn-outline-primary" for="provider_infini">
                                    <i class="fas fa-infinity me-2"></i> Infini-AI
                                </label>

                                <input type="radio" class="btn-check" name="api_provider" id="provider_zhipu" value="zhipu" {% if config.get('api_provider') == 'zhipu' %}checked{% endif %}>
                                <label class="btn btn-outline-primary" for="provider_zhipu">
                                    <i class="fas fa-brain me-2"></i> 智谱AI
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="api_model" class="form-label fw-bold">模型名称</label>
                            <input type="text" class="form-control" id="api_model" name="api_model" 
                                   value="{{ config.get('api_model', 'deepseek-r1') }}">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="api_base_url" class="form-label fw-bold">API基础URL</label>
                        <input type="url" class="form-control" id="api_base_url" name="api_base_url" 
                               value="{{ config.get('api_base_url', 'https://cloud.infini-ai.com/maas/v1/') }}">
                        <div class="form-text">
                            <div class="row">
                                <div class="col-6">
                                    <strong>智谱GLM4.5:</strong> https://open.bigmodel.cn/api/paas/v4/
                                </div>
                                <div class="col-6">
                                    <strong>Infini-AI:</strong> https://cloud.infini-ai.com/maas/v1/
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="api_keys" class="form-label fw-bold">API密钥</label>
                        <textarea class="form-control" id="api_keys" name="api_keys" rows="4" 
                                  placeholder="请输入API密钥，多个密钥请用换行分隔">{{ config.get('api_keys', [''])|join('\n') }}</textarea>
                        <div class="form-text">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>获取API密钥:</strong>
                                <a href="https://open.bigmodel.cn/" target="_blank">智谱AI开放平台</a> 或 
                                <a href="https://cloud.infini-ai.com/" target="_blank">Infini-AI平台</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 处理参数 -->
                    <h6 class="fw-bold mb-3"><i class="fas fa-sliders-h me-2"></i> 处理参数</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_workers" class="form-label">最大工作线程数</label>
                                <div class="input-group">
                                    <input type="range" class="form-range" id="max_workers_range" 
                                           min="1" max="100" value="{{ config.get('max_workers', 30) }}">
                                    <input type="number" class="form-control" id="max_workers" name="max_workers" 
                                           value="{{ config.get('max_workers', 30) }}" min="1" max="100" style="max-width: 80px;">
                                </div>
                                <div class="form-text">推荐值: 30-80</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="batch_size" class="form-label">批次大小</label>
                                <input type="number" class="form-control" id="batch_size" name="batch_size" 
                                       value="{{ config.get('batch_size', 100) }}" min="1" max="1000">
                                <div class="form-text">推荐值: 100-800</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="output_dir" class="form-label">输出目录</label>
                                <input type="text" class="form-control" id="output_dir" name="output_dir" 
                                       value="{{ config.get('output_dir', 'infini_output') }}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据类型和过滤条件 -->
                    <h6 class="fw-bold mb-3"><i class="fas fa-database me-2"></i> 数据类型配置</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="data_type" class="form-label">默认数据类型</label>
                                <select class="form-select" id="data_type" name="data_type">
                                    <option value="movie" {% if config.get('data_type') == 'movie' %}selected{% endif %}>电影数据</option>
                                    <option value="book" {% if config.get('data_type') == 'book' %}selected{% endif %}>图书数据</option>
                                    <option value="tv" {% if config.get('data_type') == 'tv' %}selected{% endif %}>电视剧数据</option>
                                </select>
                                <div class="form-text">选择默认的数据类型</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="field_to_process" class="form-label">默认处理字段</label>
                                <select class="form-select" id="field_to_process" name="field_to_process">
                                    <option value="director" {% if config.get('field_to_process') == 'director' %}selected{% endif %}>导演</option>
                                    <option value="screenwriter" {% if config.get('field_to_process') == 'screenwriter' %}selected{% endif %}>编剧</option>
                                    <option value="actor" {% if config.get('field_to_process') == 'actor' %}selected{% endif %}>演员</option>
                                </select>
                                <div class="form-text">选择默认的处理字段</div>
                            </div>
                        </div>
                        <div class="col-md-4"></div>
                    </div>

                    <h6 class="fw-bold mb-3 mt-4"><i class="fas fa-filter me-2"></i> 过滤条件</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min_reviews" class="form-label">最小评价数</label>
                                <input type="number" class="form-control" id="min_reviews" name="min_reviews" 
                                       value="{{ config.get('min_reviews', 0) }}" min="0">
                                <div class="form-text">0表示不限制</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min_short_reviews" class="form-label">最小短评数</label>
                                <input type="number" class="form-control" id="min_short_reviews" name="min_short_reviews" 
                                       value="{{ config.get('min_short_reviews', 0) }}" min="0">
                                <div class="form-text">0表示不限制</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级选项 -->
                    <h6 class="fw-bold mb-3"><i class="fas fa-cog me-2"></i> 高级选项</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="retry_refused" name="retry_refused" 
                                       {% if config.get('retry_refused', True) %}checked{% endif %}>
                                <label class="form-check-label" for="retry_refused">
                                    重试拒绝的请求
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="retry_failed" name="retry_failed" 
                                       {% if config.get('retry_failed', True) %}checked{% endif %}>
                                <label class="form-check-label" for="retry_failed">
                                    重试失败的请求
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="export_only" name="export_only" 
                                       {% if config.get('export_only', False) %}checked{% endif %}>
                                <label class="form-check-label" for="export_only">
                                    仅导出结果
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> 保存配置
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i> 测试连接
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="loadPreset()">
                            <i class="fas fa-download me-2"></i> 加载预设
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="resetConfig()">
                            <i class="fas fa-undo me-2"></i> 重置配置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 配置状态和帮助 -->
    <div class="col-md-4">
        <!-- 配置状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-check-circle"></i> 配置状态</h5>
            </div>
            <div class="card-body">
                <div id="config-validation">
                    <div class="loading-shimmer rounded" style="height: 60px;"></div>
                </div>
            </div>
        </div>
        
        <!-- 预设配置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> 预设配置</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="applyPreset('zhipu_fast')">
                        <i class="fas fa-bolt"></i> 智谱AI - 快速
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="applyPreset('zhipu_balanced')">
                        <i class="fas fa-balance-scale"></i> 智谱AI - 平衡
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="applyPreset('infini_fast')">
                        <i class="fas fa-rocket"></i> Infini-AI - 快速
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="applyPreset('infini_accurate')">
                        <i class="fas fa-crosshairs"></i> Infini-AI - 精确
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 配置说明 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-question-circle"></i> 配置说明</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="configHelp">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                API提供商
                            </button>
                        </h2>
                        <div id="help1" class="accordion-collapse collapse" data-bs-parent="#configHelp">
                            <div class="accordion-body small">
                                <strong>智谱AI:</strong> GLM4.5模型，适合中文处理<br>
                                <strong>Infini-AI:</strong> DeepSeek模型，处理速度快
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                性能参数
                            </button>
                        </h2>
                        <div id="help2" class="accordion-collapse collapse" data-bs-parent="#configHelp">
                            <div class="accordion-body small">
                                <strong>工作线程:</strong> 并发处理数量，建议30-80<br>
                                <strong>批次大小:</strong> 每次处理的记录数，建议100-800
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
                                过滤条件
                            </button>
                        </h2>
                        <div id="help3" class="accordion-collapse collapse" data-bs-parent="#configHelp">
                            <div class="accordion-body small">
                                根据电影的评价数和短评数过滤，提高数据质量
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    loadCurrentConfig();
    validateConfiguration();
    updateFieldOptions('movie'); // 初始化字段选项

    // 数据类型切换
    $('#data_type').change(function() {
        const dataType = $(this).val();
        updateFieldOptions(dataType);
    });

    // API提供商切换
    $('input[name="api_provider"]').change(function() {
        const provider = $(this).val();
        if (provider === 'zhipu') {
            $('#api_model').val('glm-4.5');
            $('#api_base_url').val('https://open.bigmodel.cn/api/paas/v4/');
        } else {
            $('#api_model').val('deepseek-r1');
            $('#api_base_url').val('https://cloud.infini-ai.com/maas/v1/');
        }
        validateConfiguration();
    });
    
    // 工作线程滑块同步
    $('#max_workers_range').on('input', function() {
        $('#max_workers').val($(this).val());
    });
    
    $('#max_workers').on('input', function() {
        $('#max_workers_range').val($(this).val());
    });
    
    // 表单提交
    $('#configForm').on('submit', function(e) {
        e.preventDefault();
        saveConfiguration();
    });
    
    // 实时验证
    $('#api_keys, #api_base_url, #api_model').on('input', function() {
        setTimeout(validateConfiguration, 500);
    });
});

function loadCurrentConfig() {
    $.get('/api/config').then(function(data) {
        if (data && data.success === false) return;

        console.log('loadCurrentConfig: 收到配置数据', data);

        // 设置API提供商 - 只有当API返回了有效数据时才设置
        if (data && data.api_provider) {
            const provider = data.api_provider;
            console.log('loadCurrentConfig: 设置API提供商为', provider);
            $(`input[name="api_provider"][value="${provider}"]`).prop('checked', true);
        } else {
            // 如果没有配置数据，默认选择智谱AI
            console.log('loadCurrentConfig: 没有API提供商配置，使用默认值zhipu');
            $(`input[name="api_provider"][value="zhipu"]`).prop('checked', true);
        }

        // 同步滑块
        const workers = data.max_workers || 30;
        $('#max_workers_range').val(workers);
        $('#max_workers').val(workers);

        // 设置其他字段
        if (data.api_model) {
            $('#api_model').val(data.api_model);
        }
        if (data.api_base_url) {
            $('#api_base_url').val(data.api_base_url);
        }
        if (data.api_keys && data.api_keys.length > 0) {
            $('#api_keys').val(data.api_keys.join('\n'));
        }
    }).catch(function(error) {
        console.error('loadCurrentConfig: 加载配置失败', error);
        // 如果加载失败，默认选择智谱AI
        $(`input[name="api_provider"][value="zhipu"]`).prop('checked', true);
    });
}

function validateConfiguration() {
    const apiKey = $('#api_keys').val().trim();
    const apiUrl = $('#api_base_url').val().trim();
    const model = $('#api_model').val().trim();
    
    let validationHtml = '';
    let allValid = true;
    
    // 验证API密钥
    if (!apiKey || apiKey === '请在此处填入您的智谱GLM4.5 API密钥') {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-times-circle text-danger me-2"></i><span>API密钥未配置</span></div>';
        allValid = false;
    } else {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-check-circle text-success me-2"></i><span>API密钥已配置</span></div>';
    }
    
    // 验证API URL
    if (!apiUrl) {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-times-circle text-danger me-2"></i><span>API URL未配置</span></div>';
        allValid = false;
    } else {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-check-circle text-success me-2"></i><span>API URL已配置</span></div>';
    }
    
    // 验证模型名称
    if (!model) {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-times-circle text-danger me-2"></i><span>模型名称未配置</span></div>';
        allValid = false;
    } else {
        validationHtml += '<div class="d-flex align-items-center mb-2"><i class="fas fa-check-circle text-success me-2"></i><span>模型名称已配置</span></div>';
    }
    
    // 总体状态
    if (allValid) {
        validationHtml += '<div class="alert alert-success mt-3 mb-0"><i class="fas fa-check me-2"></i>配置完整，可以开始处理</div>';
    } else {
        validationHtml += '<div class="alert alert-warning mt-3 mb-0"><i class="fas fa-exclamation-triangle me-2"></i>配置不完整，请检查上述项目</div>';
    }
    
    $('#config-validation').html(validationHtml);
}

function saveConfiguration() {
    const formData = new FormData($('#configForm')[0]);
    const config = {};
    
    // 构建配置对象
    for (let [key, value] of formData.entries()) {
        if (key === 'api_keys') {
            config[key] = value.split('\n').filter(key => key.trim() !== '');
        } else if (key === 'max_workers' || key === 'batch_size' || key === 'min_reviews' || key === 'min_short_reviews') {
            config[key] = parseInt(value);
        } else if (key === 'retry_refused' || key === 'retry_failed' || key === 'export_only') {
            config[key] = true;
        } else {
            config[key] = value;
        }
    }
    
    // 添加未选中的checkbox
    ['retry_refused', 'retry_failed', 'export_only'].forEach(key => {
        if (!(key in config)) {
            config[key] = false;
        }
    });
    
    // 保存配置
    const btn = $('button[type="submit"]');
    const originalText = btn.html();
    btn.html('<i class="fas fa-spinner fa-spin me-2"></i> 保存中...').prop('disabled', true);
    
    $.ajax({
        url: '/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            if (response.success) {
                showAlert('success', '配置保存成功！');
                validateConfiguration();
            } else {
                showAlert('danger', '配置保存失败：' + response.message);
            }
        },
        error: function() {
            showAlert('danger', '网络错误，请稍后重试');
        },
        complete: function() {
            btn.html(originalText).prop('disabled', false);
        }
    });
}

function testConnection() {
    const apiKey = $('#api_keys').val().split('\n')[0].trim();
    const apiUrl = $('#api_base_url').val();
    const model = $('#api_model').val();
    
    if (!apiKey || apiKey === '请在此处填入您的智谱GLM4.5 API密钥') {
        showAlert('warning', '请先配置API密钥');
        return;
    }
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 测试中...';
    btn.disabled = true;
    
    // 实际的连接测试
    $.ajax({
        url: '/api/test_connection',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            api_key: apiKey,
            api_url: apiUrl,
            model: model
        }),
        success: function(data) {
            if (data.success) {
                showAlert('success', '连接测试成功！API配置正常');
            } else {
                showAlert('danger', '连接测试失败：' + data.message);
            }
        },
        error: function() {
            showAlert('danger', '连接测试失败，请检查网络连接');
        },
        complete: function() {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    });
}

function applyPreset(presetName) {
    const presets = {
        'zhipu_fast': {
            api_provider: 'zhipu',
            api_model: 'glm-4.5',
            api_base_url: 'https://open.bigmodel.cn/api/paas/v4/',
            max_workers: 50,
            batch_size: 200,
            retry_refused: false,
            retry_failed: true
        },
        'zhipu_balanced': {
            api_provider: 'zhipu',
            api_model: 'glm-4.5',
            api_base_url: 'https://open.bigmodel.cn/api/paas/v4/',
            max_workers: 30,
            batch_size: 100,
            retry_refused: true,
            retry_failed: true
        },
        'infini_fast': {
            api_provider: 'infini',
            api_model: 'deepseek-r1',
            api_base_url: 'https://cloud.infini-ai.com/maas/v1/',
            max_workers: 80,
            batch_size: 800,
            retry_refused: false,
            retry_failed: true
        },
        'infini_accurate': {
            api_provider: 'infini',
            api_model: 'deepseek-r1',
            api_base_url: 'https://cloud.infini-ai.com/maas/v1/',
            max_workers: 20,
            batch_size: 50,
            retry_refused: true,
            retry_failed: true
        }
    };
    
    const preset = presets[presetName];
    if (preset) {
        // 应用预设值
        $(`input[name="api_provider"][value="${preset.api_provider}"]`).prop('checked', true);
        $('#api_model').val(preset.api_model);
        $('#api_base_url').val(preset.api_base_url);
        $('#max_workers').val(preset.max_workers);
        $('#max_workers_range').val(preset.max_workers);
        $('#batch_size').val(preset.batch_size);
        $('#retry_refused').prop('checked', preset.retry_refused);
        $('#retry_failed').prop('checked', preset.retry_failed);
        
        showAlert('info', `已应用 ${presetName} 预设配置`);
        validateConfiguration();
    }
}

function resetConfig() {
    if (confirm('确定要重置所有配置吗？')) {
        $('#configForm')[0].reset();
        $('#max_workers_range').val(30);
        validateConfiguration();
        showAlert('info', '配置已重置');
    }
}

function loadPreset() {
    // 这里可以实现从文件加载预设配置
    showAlert('info', '预设配置加载功能开发中...');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.col-12').first().prepend(alertHtml);
    
    // 自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

function updateFieldOptions(dataType) {
    const fieldMappings = {
        'movie': [
            { value: 'director', text: '导演' },
            { value: 'screenwriter', text: '编剧' },
            { value: 'actor', text: '演员' }
        ],
        'book': [
            { value: 'author', text: '作者' },
            { value: 'translator', text: '译者' },
            { value: 'editor', text: '编辑' }
        ],
        'tv': [
            { value: 'director', text: '导演' },
            { value: 'writer', text: '编剧' },
            { value: 'cast', text: '演员' }
        ]
    };

    const fields = fieldMappings[dataType] || fieldMappings['movie'];
    const fieldSelect = $('#field_to_process');
    const currentValue = fieldSelect.val();

    fieldSelect.empty();
    fields.forEach(field => {
        fieldSelect.append(`<option value="${field.value}">${field.text}</option>`);
    });

    // 尝试保持当前选择，如果不存在则选择第一个
    if (fields.some(f => f.value === currentValue)) {
        fieldSelect.val(currentValue);
    } else {
        fieldSelect.val(fields[0].value);
    }
}
</script>
{% endblock %}