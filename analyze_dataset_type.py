#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析dataset中数据的类型（电影 vs 电视剧）
"""

import pandas as pd
from pathlib import Path
import re
from collections import Counter

def analyze_dataset_type():
    """分析数据集类型"""
    print("=== 分析数据集类型 ===")
    
    dataset_path = Path("dataset/all_data.pkl")
    if not dataset_path.exists():
        print(f"❌ 数据文件不存在: {dataset_path}")
        return
    
    try:
        # 加载数据
        print("📊 正在加载数据...")
        df = pd.read_pickle(dataset_path)
        print(f"✅ 数据加载成功，总记录数: {len(df)}")
        
        # 1. 检查URL模式
        print(f"\n🔍 URL模式分析:")
        url_patterns = {}
        for url in df['URL'].head(100):  # 检查前100个URL
            if 'movie.douban.com' in url:
                url_patterns['movie'] = url_patterns.get('movie', 0) + 1
            elif 'tv.douban.com' in url:
                url_patterns['tv'] = url_patterns.get('tv', 0) + 1
            else:
                url_patterns['other'] = url_patterns.get('other', 0) + 1
        
        for pattern, count in url_patterns.items():
            print(f"  {pattern}: {count} 个")
        
        # 2. 检查数据类型字段
        print(f"\n📋 shujvLeixing字段分析:")
        if 'shujvLeixing' in df.columns:
            type_counts = df['shujvLeixing'].value_counts()
            print(f"  总计: {len(type_counts)} 种不同类型")
            print(f"  前10种类型:")
            for i, (type_name, count) in enumerate(type_counts.head(10).items(), 1):
                print(f"    {i:2d}. {type_name}: {count} 条")
        else:
            print("  ❌ 未找到shujvLeixing字段")
        
        # 3. 检查类型字段
        print(f"\n🎭 leixing字段分析:")
        if 'leixing' in df.columns:
            genre_counts = df['leixing'].value_counts()
            print(f"  总计: {len(genre_counts)} 种不同类型")
            print(f"  前15种类型:")
            for i, (genre, count) in enumerate(genre_counts.head(15).items(), 1):
                print(f"    {i:2d}. {genre}: {count} 条")
        else:
            print("  ❌ 未找到leixing字段")
        
        # 4. 检查标题特征
        print(f"\n📺 标题特征分析:")
        tv_keywords = ['第一季', '第二季', '第三季', '第四季', '第五季', 
                       'Season', 'season', '季', '集', 'Episode', 'episode']
        
        tv_title_count = 0
        for title in df['biaoti'].dropna():
            if any(keyword in str(title) for keyword in tv_keywords):
                tv_title_count += 1
        
        print(f"  包含电视剧关键词的标题: {tv_title_count} 条 ({tv_title_count/len(df)*100:.1f}%)")
        
        # 5. 检查具体的URL样本
        print(f"\n🔗 URL样本分析:")
        sample_urls = df['URL'].head(20).tolist()
        movie_count = 0
        tv_count = 0
        
        for url in sample_urls:
            if 'movie.douban.com' in url:
                movie_count += 1
            elif 'tv.douban.com' in url:
                tv_count += 1
        
        print(f"  前20个URL中:")
        print(f"    movie.douban.com: {movie_count} 个")
        print(f"    tv.douban.com: {tv_count} 个")
        
        # 6. 显示一些具体的URL示例
        print(f"\n📋 URL示例:")
        for i, url in enumerate(df['URL'].head(10), 1):
            domain = 'movie' if 'movie.douban.com' in url else ('tv' if 'tv.douban.com' in url else 'other')
            print(f"  {i:2d}. [{domain}] {url}")
        
        # 7. 综合判断
        print(f"\n🎯 数据类型判断:")
        
        # 基于URL判断
        all_urls = df['URL'].tolist()
        movie_urls = sum(1 for url in all_urls if 'movie.douban.com' in url)
        tv_urls = sum(1 for url in all_urls if 'tv.douban.com' in url)
        
        print(f"  基于URL域名:")
        print(f"    movie.douban.com: {movie_urls} 条 ({movie_urls/len(df)*100:.1f}%)")
        print(f"    tv.douban.com: {tv_urls} 条 ({tv_urls/len(df)*100:.1f}%)")
        
        if tv_urls > movie_urls:
            print(f"  🎬 结论: 这是一个电视剧数据集")
            data_type = 'tv'
        elif movie_urls > tv_urls:
            print(f"  🎥 结论: 这是一个电影数据集")
            data_type = 'movie'
        else:
            print(f"  🤔 结论: 混合数据集或无法确定")
            data_type = 'mixed'
        
        return data_type
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def suggest_config_for_tv():
    """为电视剧数据建议配置"""
    print(f"\n💡 电视剧数据处理建议:")
    print(f"  1. 修改配置文件中的data_type为'tv'")
    print(f"  2. 电视剧支持的字段:")
    print(f"     - director: 导演")
    print(f"     - writer: 编剧")
    print(f"     - cast: 演员")
    print(f"  3. 字段映射关系:")
    print(f"     - director -> daoyan")
    print(f"     - writer -> bianjv")
    print(f"     - cast -> zhuyan")
    
    # 检查当前配置
    config_path = Path("infini_config.json")
    if config_path.exists():
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"\n📋 当前配置:")
        print(f"  field_to_process: {config.get('field_to_process', '未设置')}")
        print(f"  data_type: {config.get('data_type', '未设置（默认为movie）')}")
        
        if config.get('data_type') != 'tv':
            print(f"\n⚠️  建议修改配置:")
            print(f"  需要在配置文件中添加: \"data_type\": \"tv\"")

def main():
    """主函数"""
    print("🔍 开始分析数据集类型...")
    
    data_type = analyze_dataset_type()
    
    if data_type == 'tv':
        suggest_config_for_tv()
    elif data_type == 'movie':
        print(f"\n💡 这是电影数据集，当前配置应该适用")
    
    print(f"\n✅ 分析完成")

if __name__ == "__main__":
    main()
