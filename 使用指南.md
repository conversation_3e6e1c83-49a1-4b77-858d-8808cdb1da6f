# 豆瓣电影数据处理工具 - 使用指南

## 🎯 问题解决

刚才遇到的API路由错误已经修复，现在所有功能都可以正常使用了。

## 🚀 快速开始

### 1. 启动Web应用

```bash
python run_web.py
```

### 2. 访问Web界面

打开浏览器访问：http://localhost:5000

### 3. 配置API密钥

1. 点击左侧菜单的"配置管理"
2. 选择API提供商（推荐"智谱AI"）
3. 填入您的智谱GLM4.5 API密钥
4. 点击"保存配置"

**智谱GLM4.5 API配置：**
- API提供商：zhipu
- 模型名称：glm-4.5
- 基础URL：https://open.bigmodel.cn/api/paas/v4/
- 获取API密钥：https://open.bigmodel.cn/

### 4. 上传数据文件

1. 点击"文件上传"
2. 选择CSV或PKL格式的数据文件
3. 点击"上传文件"

### 5. 开始处理

1. 上传成功后会自动跳转到处理页面
2. 选择要处理的字段类型（导演/编剧/演员）
3. 设置过滤条件（可选）
4. 点击"开始处理"
5. 等待处理完成

### 6. 查看结果

1. 点击"处理结果"查看生成的文件
2. 可以下载CSV或JSON格式的结果

## 📋 数据文件格式

### CSV文件示例：
```csv
ID,biaoti,daoyan,bianjv,zhuyan
1,肖申克的救赎,弗兰克·德拉邦特,弗兰克·德拉邦特,蒂姆·罗宾斯/摩根·弗里曼
2,霸王别姬,陈凯歌,芦苇/李碧华,张国荣/张丰毅/巩俐
```

### 字段说明：
- `ID`: 电影唯一标识符
- `biaoti`: 电影标题
- `daoyan`: 导演姓名（多人用/分隔）
- `bianjv`: 编剧姓名（多人用/分隔）
- `zhuyan`: 主要演员（多人用/分隔）

## 🔧 配置参数说明

```json
{
    "api_provider": "zhipu",              // API提供商
    "api_keys": ["您的API密钥"],           // API密钥列表
    "api_base_url": "https://open.bigmodel.cn/api/paas/v4/",  // API地址
    "api_model": "glm-4.5",               // 模型名称
    "max_workers": 80,                     // 最大工作线程数
    "batch_size": 800,                    // 批次大小
    "output_dir": "infini_output",       // 输出目录
    "min_reviews": 0,                     // 最小评价数过滤
    "min_short_reviews": 10,              // 最小短评数过滤
    "retry_refused": false,               // 是否重试拒绝的请求
    "retry_failed": false,                // 是否重试失败的请求
    "export_only": false,                 // 是否仅导出结果
    "field_to_process": "actor"           // 默认处理字段
}
```

## 🎨 Web界面功能

### 首页
- 系统概览
- 快速操作入口
- 实时状态显示

### 配置管理
- API配置
- 处理参数设置
- 连接测试

### 文件上传
- 拖拽上传支持
- 文件格式验证
- 上传历史记录

### 数据处理
- 实时进度显示
- 处理状态监控
- 错误处理

### 结果查看
- 文件列表展示
- 在线预览
- 批量下载

## 🛠️ 常见问题

### Q: 如何获取智谱GLM4.5 API密钥？
A: 访问 https://open.bigmodel.cn/ 注册账号并获取API密钥。

### Q: 处理过程中出现错误怎么办？
A: 系统会自动重试失败的请求。如果仍然失败，请检查：
- API密钥是否正确
- 网络连接是否正常
- API配额是否充足

### Q: 支持哪些文件格式？
A: 目前支持CSV和PKL格式的文件。

### Q: 如何提高处理速度？
A: 可以适当增加 `max_workers` 和 `batch_size` 参数，但要注意API限制。

### Q: 处理结果在哪里？
A: 结果保存在 `infini_output` 目录中，也可以在Web界面的"处理结果"页面查看。

## 📞 技术支持

如果遇到问题，请检查：
1. Web应用是否正常启动
2. API密钥是否正确配置
3. 数据文件格式是否正确
4. 网络连接是否正常

## 🎉 恭喜！

现在您已经拥有一个功能完整的豆瓣电影数据处理工具，支持智谱GLM4.5 API，具有直观的Web界面！

开始使用吧！ 🚀