#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""
import json
import sys
from pathlib import Path

def test_config():
    """测试配置文件"""
    config_file = "infini_config.json"
    
    if not Path(config_file).exists():
        print(f"配置文件 {config_file} 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("配置文件加载成功")
        
        # 检查必要字段
        required_fields = ["api_provider", "api_keys", "api_base_url", "api_model"]
        for field in required_fields:
            if field not in config:
                print(f"缺少必要字段: {field}")
                return False
        
        print("配置文件字段检查通过")
        
        # 检查API密钥
        api_keys = config.get("api_keys", [])
        if not api_keys or api_keys[0] == "请在此处填入您的智谱GLM4.5 API密钥":
            print("请配置API密钥")
            return False
        
        print("API密钥配置检查通过")
        
        # 显示配置信息
        print("\n当前配置:")
        print(f"   API提供商: {config['api_provider']}")
        print(f"   模型名称: {config['api_model']}")
        print(f"   基础URL: {config['api_base_url']}")
        print(f"   API密钥数量: {len(api_keys)}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"配置文件读取错误: {e}")
        return False

def test_directories():
    """测试必要目录"""
    directories = ["uploads", "infini_output", "templates"]
    
    for directory in directories:
        if not Path(directory).exists():
            print(f"目录 {directory} 不存在")
            return False
        print(f"目录 {directory} 存在")
    
    return True

def test_templates():
    """测试模板文件"""
    template_files = [
        "templates/base.html",
        "templates/index.html",
        "templates/config.html",
        "templates/upload.html",
        "templates/process.html",
        "templates/results.html"
    ]
    
    for template_file in template_files:
        if not Path(template_file).exists():
            print(f"模板文件 {template_file} 不存在")
            return False
        print(f"模板文件 {template_file} 存在")
    
    return True

def main():
    """主测试函数"""
    print("开始测试Web应用配置...\n")
    
    tests = [
        ("配置文件", test_config),
        ("目录结构", test_directories),
        ("模板文件", test_templates)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}:")
        if not test_func():
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("所有测试通过！Web应用可以启动。")
        print("\n启动命令:")
        print("   python run_web.py")
        print("\n访问地址:")
        print("   http://localhost:5000")
    else:
        print("部分测试失败，请检查配置。")
        sys.exit(1)

if __name__ == "__main__":
    main()