#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript修复效果
"""

import requests
import json
import time

def test_api_responses():
    """测试API响应是否包含可能导致JavaScript错误的数据"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 测试API响应数据格式...")
    
    # 测试dataset_info端点
    try:
        response = requests.get(f"{base_url}/api/dataset_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                info = data['data']
                
                print(f"📊 数据集信息API测试:")
                print(f"  ✅ API响应成功")
                
                # 检查可能的问题数据
                issues = []
                
                # 检查sample_data中的NaN值
                if 'sample_data' in info:
                    for i, record in enumerate(info['sample_data'][:3]):
                        nan_fields = [k for k, v in record.items() if v is None or (isinstance(v, float) and str(v) == 'nan')]
                        if nan_fields:
                            issues.append(f"样本数据{i+1}包含NaN字段: {len(nan_fields)}个")
                
                # 检查数值字段
                numeric_fields = ['total_records', 'size_mb']
                for field in numeric_fields:
                    if field in info:
                        value = info[field]
                        if isinstance(value, float) and str(value) == 'nan':
                            issues.append(f"{field}字段包含NaN值")
                
                if issues:
                    print(f"  ⚠️  发现潜在问题:")
                    for issue in issues:
                        print(f"     - {issue}")
                else:
                    print(f"  ✅ 数据格式正常，无NaN值问题")
                
                # 显示关键信息
                print(f"  📈 记录数: {info.get('total_records', 'N/A')}")
                print(f"  📁 文件大小: {info.get('size_mb', 'N/A')} MB")
                print(f"  📋 字段数: {len(info.get('columns', []))}")
                
            else:
                print(f"  ❌ API返回失败: {data.get('message')}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 请求异常: {str(e)}")
    
    # 测试其他可能有问题的端点
    other_endpoints = ['/api/tasks', '/api/system_stats', '/api/performance_metrics']
    
    for endpoint in other_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"📡 {endpoint}: ✅ 响应正常")
            else:
                print(f"📡 {endpoint}: ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"📡 {endpoint}: ❌ 异常 {str(e)}")

def test_web_page():
    """测试Web页面是否可以正常加载"""
    base_url = "http://127.0.0.1:5000"
    
    print(f"\n🌐 测试Web页面加载...")
    
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print(f"  ✅ 主页加载成功")
            
            # 检查页面是否包含关键元素
            content = response.text
            if 'loadDashboardData' in content:
                print(f"  ✅ JavaScript函数存在")
            else:
                print(f"  ⚠️  JavaScript函数可能缺失")
                
            if 'Promise.all' in content:
                print(f"  ✅ Promise处理代码存在")
            else:
                print(f"  ⚠️  Promise处理代码可能缺失")
                
        else:
            print(f"  ❌ 主页加载失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"  ❌ 页面加载异常: {str(e)}")

def provide_usage_instructions():
    """提供使用说明"""
    print(f"\n💡 使用说明:")
    print(f"  1. 打开浏览器访问: http://127.0.0.1:5000")
    print(f"  2. 如果仍有JavaScript错误，请:")
    print(f"     - 按F12打开开发者工具")
    print(f"     - 查看Console标签页的错误信息")
    print(f"     - 尝试硬刷新页面 (Ctrl+F5)")
    print(f"     - 清除浏览器缓存")
    
    print(f"\n🎯 电视剧数据处理:")
    print(f"  1. 点击'使用内置数据集'按钮")
    print(f"  2. 在处理页面选择:")
    print(f"     - 数据类型: TV (电视剧)")
    print(f"     - 处理字段: director/writer/cast")
    print(f"     - 最小评价数: 0")
    print(f"     - 最小短评数: 10")
    print(f"  3. 点击'开始处理'")
    
    print(f"\n📋 可用字段:")
    print(f"  - director (导演): 58,194条数据")
    print(f"  - writer (编剧): 47,917条数据")
    print(f"  - cast (演员): 75,091条数据")

def main():
    """主函数"""
    print("🚀 测试JavaScript修复效果...")
    
    # 等待服务器稳定
    time.sleep(2)
    
    test_api_responses()
    test_web_page()
    provide_usage_instructions()
    
    print(f"\n✅ 测试完成")
    print(f"🌐 Web界面: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
