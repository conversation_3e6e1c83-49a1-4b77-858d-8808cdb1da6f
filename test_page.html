<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 数据集API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>数据集API测试页面</h1>
    <p>这个页面用于测试API连接和数据加载</p>
    
    <button onclick="testAPI()">测试API连接</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testAPI() {
            addResult('开始测试API连接...', 'info');
            
            // 测试基本连接
            $.get('/api/dataset_info')
                .done(function(data) {
                    addResult('✅ API连接成功', 'success');
                    
                    if (data.success) {
                        const info = data.data;
                        addResult(`📊 数据集信息: ${info.total_records} 条记录, ${info.size_mb} MB`, 'success');
                        
                        // 检查NaN值处理
                        if (info.sample_data && info.sample_data.length > 0) {
                            const sample = info.sample_data[0];
                            const nanFields = Object.keys(sample).filter(key => {
                                const value = sample[key];
                                return value === null || (typeof value === 'number' && isNaN(value));
                            });
                            
                            if (nanFields.length > 0) {
                                addResult(`⚠️ 发现 ${nanFields.length} 个NaN/null字段，但JavaScript正确处理了`, 'info');
                            } else {
                                addResult('✅ 样本数据无NaN值', 'success');
                            }
                        }
                        
                        // 测试字段统计
                        if (info.field_stats) {
                            Object.entries(info.field_stats).forEach(([field, stats]) => {
                                const fieldName = field === 'daoyan' ? '导演' : 
                                                 field === 'bianjv' ? '编剧' : '演员';
                                addResult(`📋 ${fieldName}(${field}): ${stats.total} 条记录`, 'success');
                            });
                        }
                        
                    } else {
                        addResult(`❌ API返回失败: ${data.message}`, 'error');
                    }
                })
                .fail(function(xhr, status, error) {
                    addResult(`❌ API请求失败: ${error}`, 'error');
                    addResult(`状态: ${status}, 响应码: ${xhr.status}`, 'error');
                    
                    if (xhr.status === 0) {
                        addResult('可能的原因: 服务器未运行或网络连接问题', 'error');
                    } else if (xhr.status === 404) {
                        addResult('可能的原因: API端点不存在', 'error');
                    } else if (xhr.status === 500) {
                        addResult('可能的原因: 服务器内部错误', 'error');
                    }
                });
        }

        // 页面加载时自动测试
        $(document).ready(function() {
            addResult('页面加载完成，jQuery已就绪', 'info');
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
