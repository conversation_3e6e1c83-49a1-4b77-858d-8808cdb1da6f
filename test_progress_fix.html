<!DOCTYPE html>
<html>
<head>
    <title>Test Progress Display Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h3>左侧栏进度显示测试</h3>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <p>这是主要内容区域</p>
                        <button class="btn btn-primary" onclick="simulateProgress()">模拟进度更新</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list"></i> 处理队列</h5>
                    </div>
                    <div class="card-body">
                        <div id="taskQueue">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟任务数据
        let mockTasks = [
            {
                task_id: "task_20250830_013903",
                status: "running",
                filename: "all_data.pkl",
                progress: 25,
                message: "正在处理: 第25/100项",
                start_time: "2025-08-30T01:39:04.180445"
            },
            {
                task_id: "test_progress_2dada93f", 
                status: "completed",
                filename: "test_progress.csv",
                progress: 100,
                message: "处理完成",
                start_time: "2025-08-30T01:28:19.864787"
            }
        ];

        function loadTaskQueue() {
            console.log('loadTaskQueue: 开始加载任务队列');
            
            let html = '';
            if (mockTasks.length === 0) {
                html = '<small class="text-muted">暂无处理任务</small>';
            } else {
                mockTasks.forEach(function(task) {
                    console.log('loadTaskQueue: 处理任务', task);
                    
                    let statusClass = task.status === 'running' ? 'status-running' : 
                                     task.status === 'completed' ? 'status-completed' : 'status-error';
                    let statusIcon = task.status === 'running' ? 'fa-spinner fa-spin' : 
                                     task.status === 'completed' ? 'fa-check-circle' : 'fa-exclamation-circle';
                    
                    // 修复的进度显示逻辑
                    let progressHtml = '';
                    if (task.status === 'running' && task.progress !== undefined) {
                        const progressPercent = Math.round(task.progress);
                        progressHtml = `
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-success" style="width: ${progressPercent}%"></div>
                            </div>
                            <small class="text-muted">${progressPercent}% - ${task.message || '正在处理...'}</small>
                        `;
                    } else if (task.status === 'completed') {
                        progressHtml = `<small class="text-success">已完成 - ${task.message || '处理完成'}</small>`;
                    } else if (task.status === 'error') {
                        progressHtml = `<small class="text-danger">错误: ${task.error || '未知错误'}</small>`;
                    } else if (task.status === 'running') {
                        progressHtml = `<small class="text-info">正在运行... - ${task.message || '正在处理'}</small>`;
                    }
                    
                    html += `
                        <div class="mb-2 p-2 border rounded">
                            <div class="d-flex justify-content-between">
                                <small class="fw-bold">${task.filename}</small>
                                <i class="fas ${statusIcon} ${statusClass}"></i>
                            </div>
                            <small class="text-muted">${new Date(task.start_time).toLocaleString()}</small>
                            ${progressHtml}
                        </div>
                    `;
                });
            }
            
            console.log('loadTaskQueue: 更新HTML');
            $('#taskQueue').html(html);
        }

        function simulateProgress() {
            // 模拟进度更新
            if (mockTasks[0].status === 'running') {
                mockTasks[0].progress += 10;
                if (mockTasks[0].progress >= 100) {
                    mockTasks[0].progress = 100;
                    mockTasks[0].status = 'completed';
                    mockTasks[0].message = '处理完成';
                } else {
                    mockTasks[0].message = `正在处理: 第${mockTasks[0].progress}/100项`;
                }
                loadTaskQueue();
            }
        }

        // 初始化
        $(document).ready(function() {
            loadTaskQueue();
            
            // 定时刷新
            setInterval(function() {
                loadTaskQueue();
            }, 3000);
        });
    </script>
</body>
</html>