{% extends "base.html" %}

{% block title %}系统日志 - 豆瓣电影数据处理工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2>
                    <i class="fas fa-file-alt me-2"></i> 系统日志管理
                </h2>
                <p class="text-muted mb-0">查看和管理系统运行日志</p>
            </div>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <div class="btn-group">
                    <button class="btn btn-outline-danger dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-trash"></i> 清理日志
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="clearLogs('errors')">清理错误日志</a></li>
                        <li><a class="dropdown-item" href="#" onclick="clearLogs('app')">清理应用日志</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="clearLogs('all')">清理所有日志</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志统计 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card danger">
            <div class="stat-number text-danger" id="error-count">--</div>
            <div class="stat-label">错误数量</div>
            <div class="small text-muted mt-1" id="error-detail">最近24小时</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number text-warning" id="warning-count">--</div>
            <div class="stat-label">警告数量</div>
            <div class="small text-muted mt-1" id="warning-detail">最近24小时</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card info">
            <div class="stat-number text-info" id="info-count">--</div>
            <div class="stat-label">信息数量</div>
            <div class="small text-muted mt-1" id="info-detail">最近24小时</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card primary">
            <div class="stat-number text-primary" id="total-logs">--</div>
            <div class="stat-label">日志总数</div>
            <div class="small text-muted mt-1" id="total-detail">当前显示</div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> 日志记录</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="logLevel" onchange="filterLogs()">
                            <option value="all">所有级别</option>
                            <option value="ERROR">错误</option>
                            <option value="WARNING">警告</option>
                            <option value="INFO">信息</option>
                        </select>
                        <select class="form-select form-select-sm" id="logLimit" onchange="refreshLogs()">
                            <option value="50">最近50条</option>
                            <option value="100" selected>最近100条</option>
                            <option value="200">最近200条</option>
                            <option value="500">最近500条</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 600px;">
                    <table class="table table-hover mb-0">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="180px">时间</th>
                                <th width="80px">级别</th>
                                <th width="120px">类型</th>
                                <th>消息</th>
                                <th width="100px">操作</th>
                            </tr>
                        </thead>
                        <tbody id="logs-tbody">
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="loading-shimmer rounded" style="height: 40px;"></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情弹窗 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="log-detail-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allLogs = [];

$(document).ready(function() {
    loadLogs();
    
    // 自动刷新
    setInterval(function() {
        loadLogs();
    }, 10000);
});

function loadLogs() {
    const limit = $('#logLimit').val() || 100;
    
    $.get(`/api/logs?limit=${limit}`).then(function(data) {
        if (data.success) {
            allLogs = data.logs;
            updateLogStats(data.logs);
            filterLogs();
        } else {
            $('#logs-tbody').html(`
                <tr>
                    <td colspan="5" class="text-center text-danger py-4">
                        <i class="fas fa-exclamation-triangle"></i> 加载日志失败: ${data.message}
                    </td>
                </tr>
            `);
        }
    });
}

function updateLogStats(logs) {
    const last24h = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const recentLogs = logs.filter(log => {
        const logTime = new Date(log.timestamp);
        return logTime >= last24h;
    });
    
    const errorCount = recentLogs.filter(log => log.level === 'ERROR').length;
    const warningCount = recentLogs.filter(log => log.level === 'WARNING').length;
    const infoCount = recentLogs.filter(log => log.level === 'INFO').length;
    
    $('#error-count').text(errorCount);
    $('#warning-count').text(warningCount);
    $('#info-count').text(infoCount);
    $('#total-logs').text(logs.length);
}

function filterLogs() {
    const level = $('#logLevel').val();
    let filteredLogs = allLogs;
    
    if (level !== 'all') {
        filteredLogs = allLogs.filter(log => log.level === level);
    }
    
    displayLogs(filteredLogs);
}

function displayLogs(logs) {
    let html = '';
    
    if (logs.length === 0) {
        html = `
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无日志记录
                </td>
            </tr>
        `;
    } else {
        logs.forEach(function(log, index) {
            let levelBadge = '';
            switch(log.level) {
                case 'ERROR':
                    levelBadge = '<span class="badge bg-danger">错误</span>';
                    break;
                case 'WARNING':
                    levelBadge = '<span class="badge bg-warning">警告</span>';
                    break;
                case 'INFO':
                    levelBadge = '<span class="badge bg-info">信息</span>';
                    break;
                default:
                    levelBadge = '<span class="badge bg-secondary">其他</span>';
            }
            
            const timestamp = new Date(log.timestamp).toLocaleString();
            const message = log.message.length > 100 ? 
                log.message.substring(0, 100) + '...' : log.message;
            
            html += `
                <tr>
                    <td class="small">${timestamp}</td>
                    <td>${levelBadge}</td>
                    <td><span class="badge bg-light text-dark">${log.type || 'APP_LOG'}</span></td>
                    <td class="small">${escapeHtml(message)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="showLogDetail(${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#logs-tbody').html(html);
}

function showLogDetail(index) {
    const log = allLogs[index];
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <strong>时间:</strong> ${new Date(log.timestamp).toLocaleString()}
            </div>
            <div class="col-md-6">
                <strong>级别:</strong> <span class="badge ${log.level === 'ERROR' ? 'bg-danger' : log.level === 'WARNING' ? 'bg-warning' : 'bg-info'}">${log.level}</span>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <strong>类型:</strong> ${log.type || 'APP_LOG'}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <strong>消息:</strong>
                <pre class="mt-2 p-2 bg-light rounded small">${escapeHtml(log.message)}</pre>
            </div>
        </div>
        ${log.context ? `
            <div class="row mt-2">
                <div class="col-12">
                    <strong>上下文:</strong>
                    <pre class="mt-2 p-2 bg-light rounded small">${JSON.stringify(log.context, null, 2)}</pre>
                </div>
            </div>
        ` : ''}
    `;
    
    $('#log-detail-content').html(html);
    $('#logDetailModal').modal('show');
}

function refreshLogs() {
    $('#logs-tbody').html(`
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="loading-shimmer rounded" style="height: 40px;"></div>
            </td>
        </tr>
    `);
    loadLogs();
}

function clearLogs(type) {
    const confirmMsg = type === 'all' ? '确定要清理所有日志吗？' : `确定要清理${type === 'errors' ? '错误' : '应用'}日志吗？`;
    
    if (confirm(confirmMsg)) {
        if (type === 'all') {
            Promise.all([
                $.post('/api/clear_logs', JSON.stringify({type: 'errors'}), 'json'),
                $.post('/api/clear_logs', JSON.stringify({type: 'app'}), 'json')
            ]).then(() => {
                alert('所有日志已清理');
                loadLogs();
            });
        } else {
            $.ajax({
                url: '/api/clear_logs',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({type: type}),
                success: function(data) {
                    if (data.success) {
                        alert(data.message);
                        loadLogs();
                    } else {
                        alert('清理失败: ' + data.message);
                    }
                }
            });
        }
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}