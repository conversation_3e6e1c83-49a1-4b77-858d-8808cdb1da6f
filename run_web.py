#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web应用启动脚本
"""
import os
import sys
import logging
from web_app import app

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/web_app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """启动Web应用"""
    logger.info("正在启动豆瓣电影数据处理Web应用...")
    
    # 检查必要的目录
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('infini_output', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 启动Flask应用
    logger.info("Web应用启动成功！")
    logger.info("访问地址: http://localhost:5000")
    logger.info("按 Ctrl+C 停止服务")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("Web应用已停止")
    except Exception as e:
        logger.error(f"启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()