#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dataset目录中的数据集
"""

import pandas as pd
import json
from pathlib import Path
import sys
import os

def test_dataset():
    """测试dataset中的数据"""
    print("=== 测试dataset中的数据 ===")
    
    # 检查数据文件是否存在
    dataset_path = Path("dataset/all_data.pkl")
    if not dataset_path.exists():
        print(f"❌ 数据文件不存在: {dataset_path}")
        return False
    
    print(f"✅ 找到数据文件: {dataset_path}")
    print(f"📁 文件大小: {dataset_path.stat().st_size / 1024 / 1024:.2f} MB")
    
    try:
        # 加载数据
        print("\n📊 正在加载数据...")
        df = pd.read_pickle(dataset_path)
        print(f"✅ 数据加载成功")
        print(f"📈 总记录数: {len(df)}")
        print(f"📋 列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n📝 数据列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 显示数据类型
        print(f"\n🔍 数据类型:")
        for col in df.columns:
            dtype = str(df[col].dtype)
            non_null = df[col].notna().sum()
            null_count = df[col].isna().sum()
            print(f"  {col:15s}: {dtype:10s} (非空: {non_null:6d}, 空值: {null_count:6d})")
        
        # 检查关键字段
        key_fields = ['biaoti', 'daoyan', 'bianjv', 'zhuyan', 'pingjiaShu']
        print(f"\n🎯 关键字段检查:")
        for field in key_fields:
            if field in df.columns:
                non_null = df[field].notna().sum()
                sample_data = df[field].dropna().head(3).tolist()
                print(f"  ✅ {field:12s}: {non_null:6d} 条有效数据")
                if sample_data:
                    print(f"     示例: {sample_data}")
            else:
                print(f"  ❌ {field:12s}: 字段不存在")
        
        # 显示前几行数据
        print(f"\n📋 前3行数据预览:")
        print(df.head(3).to_string())
        
        # 检查评价数筛选
        if 'pingjiaShu' in df.columns:
            print(f"\n📊 评价数统计:")
            print(f"  总记录数: {len(df)}")
            
            # 尝试不同的评价数阈值
            for min_reviews in [0, 10, 100, 1000]:
                try:
                    # 转换为数值类型
                    pingjiaShu_numeric = pd.to_numeric(df['pingjiaShu'], errors='coerce')
                    filtered_count = (pingjiaShu_numeric >= min_reviews).sum()
                    print(f"  评价数 >= {min_reviews:4d}: {filtered_count:6d} 条记录")
                except Exception as e:
                    print(f"  ❌ 评价数 >= {min_reviews:4d}: 处理失败 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 加载数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    config_path = Path("infini_config.json")
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 配置文件加载成功")
        print(f"📋 配置内容:")
        for key, value in config.items():
            if key == 'api_keys':
                # 隐藏API密钥
                masked_keys = [f"{key[:8]}***{key[-4:]}" for key in value]
                print(f"  {key:20s}: {masked_keys}")
            else:
                print(f"  {key:20s}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {str(e)}")
        return False

def test_processor_import():
    """测试处理器导入"""
    print("\n=== 测试处理器导入 ===")
    
    try:
        from infini_processor_v2 import InfiniDirectorProcessor
        print("✅ InfiniDirectorProcessor 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试dataset数据集...")
    
    # 测试数据集
    dataset_ok = test_dataset()
    
    # 测试配置
    config_ok = test_config()
    
    # 测试处理器导入
    processor_ok = test_processor_import()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"数据集测试: {'✅ 通过' if dataset_ok else '❌ 失败'}")
    print(f"配置测试:   {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"处理器测试: {'✅ 通过' if processor_ok else '❌ 失败'}")
    
    if dataset_ok and config_ok and processor_ok:
        print(f"\n🎉 所有测试通过！可以使用dataset中的数据进行处理。")
        print(f"\n💡 使用建议:")
        print(f"  1. 通过Web界面: 访问 http://localhost:5000，选择'使用内置数据集'")
        print(f"  2. 通过命令行: python infini_processor_v2.py --data_path dataset/all_data.pkl")
        print(f"  3. 可以调整配置文件中的参数来控制处理行为")
    else:
        print(f"\n❌ 存在问题，请检查上述错误信息")
    
    return dataset_ok and config_ok and processor_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
